import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:schnell_pole_installation/utils/dio_client.dart';
import '../env.dart';

class SurveyService {
  String? status;

  Future<String> updateSurveyDetailService(data) async {
    log('postdata: $data');
    Dio dio = DioClient.dio;
    var url = '';
    if (data['assetType'] == 'Pole') {
      url = '$baseUrl/api/pole/install/';
    } else if (data['assetType'] == 'Switch Point') {
      url = '$baseUrl/api/switch_point/install/';
    } else if (data['assetType'] == 'Transformer') {
      url = '$baseUrl/api/transformer/install/';
    } else {
      url = '$baseUrl/api/pole/install/';
    }
    try {
      Response response = await dio.post(url,
          data: data,
          options: Options(contentType: 'application/json', headers: {
            // "token": token,
          }));
      if (response.data != "") {
        var result = jsonDecode(response.data);
        log('$result');
        status = result['status'].toString();
      }
      return status ?? 'Unknown';
    } catch (e) {
      log('$e');
      return 'Something Went Wrong';
    }
  }
}
