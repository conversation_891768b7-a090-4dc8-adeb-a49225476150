import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:camera/camera.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_internet_signal/flutter_internet_signal.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as r;
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart' as appsettings;
import 'package:provider/provider.dart';
import 'package:schnell_pole_installation/Pole_Details/pole_details_controller.dart';
import 'package:schnell_pole_installation/Provider/provider.dart';
import 'package:schnell_pole_installation/survey/models/pole_survey_model.dart';
import 'package:schnell_pole_installation/survey/models/sp_survey_model.dart';
import 'package:schnell_pole_installation/survey/models/survey_image_model.dart';
import 'package:schnell_pole_installation/survey/models/transformer_model.dart';
import 'package:schnell_pole_installation/survey/s3_upload_service.dart';
import 'package:schnell_pole_installation/survey/screens/base_survey.dart';
import 'package:schnell_pole_installation/survey/survey_service.dart';
import 'package:schnell_pole_installation/utils/constants.dart';
import 'package:schnell_pole_installation/utils/loader.dart';
import 'package:schnell_pole_installation/utils/utility.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:carrier_info/carrier_info.dart';
import 'package:permission_handler/permission_handler.dart';

final surveyController =
    r.ChangeNotifierProvider<SurveyProvider>((ref) => SurveyProvider());

class SurveyProvider extends ChangeNotifier {
  late PoleSurveyModel _surveyDetails;
  late SpSurveyModel _spSurveyDetails;
  late TransformerSurveyModel _transSurveyDetails;
  final PoleDetailController _poleDetailController = PoleDetailController();
  final SurveyService _service = SurveyService();
  final S3UploadService _s3UploadService = S3UploadService();
  late MultiCapturedImageModel _imageDetails;
  final TextEditingController _clampTypeLengthController =
      TextEditingController();
  final TextEditingController _clampTypeWidthController =
      TextEditingController();
  final TextEditingController _goodArmTextController =
      TextEditingController(text: '0');
  final TextEditingController _badArmTextController =
      TextEditingController(text: '0');
  final TextEditingController _missingArmTextController =
      TextEditingController(text: '1');
  final TextEditingController _workingTextController =
      TextEditingController(text: '0');
  final TextEditingController _notWorkingTextController =
      TextEditingController(text: '0');
  final TextEditingController _missingTextController =
      TextEditingController(text: '1');
  final TextEditingController _poleNumberController = TextEditingController();
  final TextEditingController _exCorpPoleNoController = TextEditingController();
  final TextEditingController _switchPointController = TextEditingController();
  final TextEditingController _switchPointNumberController =
      TextEditingController();
  final TextEditingController _commentsController = TextEditingController();
  final TextEditingController _surveyManualLocationEntry =
      TextEditingController();

  final TextEditingController _rrNumberController = TextEditingController();
  final TextEditingController _spMeterNoController = TextEditingController();
  final TextEditingController _spMakeController =
      TextEditingController(text: 'L&T');
  final TextEditingController _spConnectedLoadController =
      TextEditingController();
  final TextEditingController _switchPointIdController =
      TextEditingController();

  final TextEditingController _transNoController = TextEditingController();
  final TextEditingController _transCapacityController =
      TextEditingController();

  bool _isSurveyLocationFetched = false;
  String _clickEventIsFor = '';
  String _fetchedLat = '';
  String _fetchedLong = '';
  String _fetchedAcc = '';
  String _fetchedAltitude = '';
  String _fetchedLandMark = '';
  String _selectedTrafficDensity = 'High';
  String _selectedTrafficSpeed = 'Slow Moving';
  String _selectedPoleNumber = '';
  String _selectedSwitchPointNo = '';
  String _selectedEscomPoleNumber = '';
  String _selectedRoadType = 'Main Road';
  String _selectedAssetType = 'Switch Point';
  String _selectedPoleType = 'RCC';
  String _selectedPoleHeight = '7 m';
  String _selectedPoleSpan = '';
  String _selectedPoleCondition = 'Good';
  String _selectedConnectionType = 'Connection Type';
  String _selectedArmCount = '1';
  String _selectedArmCondition = 'Good';
  String _selectedArmLength = 'Normal';
  // bool _isClampRequired = true;
  bool _isEarthingRequired = true;
  bool _isBracketRequired = true;
  bool _isControlWireStatus = true;
  String _selectedManualSwitchCtrl = 'Exists - Good Condition';
  String _selectedWorkingCount = '0';
  String _selectedNotWorkingCount = '0';
  String _selectedMissingCount = '0';
  String _selectedGoodArmCount = '0';
  String _selectedBadArmCount = '0';
  String _selectedMissingArmCount = '0';
  String _selectedLampType = 'Tube light';
  String _selectedLampWatts = '40 W';
  List<Map<String, String>> _selectedLamps = [
    {'type': 'Tube light', 'watts': '40 W'}
  ];
  String _selectedComments = '';
  String _selectedManualEnteredLocation = '';
  String _selectedRoadCategory = 'A1';
  String _selectedRoadWidth = 'above 9m';
  String _selectedVehicleAccess = 'Long Truck';
  String _selectedIncomingTransLine = 'Under Ground';
  String _selectedIncomingTransType = 'Dedicated for Streetlight';
  String _selectedBracketMountingHeight = '6 m';
  String _selectedCustomBracketHeight = '';
  String _selectedClampType = '6 x 3 inch';
  String _selectedClampLength = '';
  String _selectedClampWidth = '';
  String _selectedClampUnits = 'inch';
  String _selectedPoleRatingUnits = 'hp';
  String _selectedPoleWinchCondition = 'Good';
  String _selectedPoleRopeCondition = 'Working';
  String _selectedPoleMotorCondition = 'Good';
  final TextEditingController _poleMotorMakeController =
      TextEditingController();
  final TextEditingController _poleMotorModelController =
      TextEditingController();
  final TextEditingController _poleMotorRatingController =
      TextEditingController();
  final TextEditingController _poleTransformerNoController =
      TextEditingController();
  final TextEditingController _spTransformerNoController =
      TextEditingController();
  String _selectedSwitchPointType = 'Feeder Pillar';
  String _selectedSPMeter = 'Working';
  String _selectedSpEarthingCondition = 'Good';
  String _selectedSpCondition = 'Working';
  String _selectedSpMeterType = 'Digital';
  String _selectedSpMake = 'L&T';
  String _selectedSpPhase = '1';
  String _selectedSpNumber = '';
  String _selectedSpRrNo = '';
  String _selectedSpId = '';
  String _selectedSpConnectedLoad = '';
  String _selectedSpMeterNo = '';
  String _carrierName = '';
  int _signalStrengthLevel = 0;

  updateSurveyFieldValues(
    context,
    ref,
    dynamic onchangedValue,
    String? fieldName,
  ) {
    if (fieldName == null) return;

    switch (fieldName) {
      case 'trafficDensity':
        _selectedTrafficDensity = onchangedValue;
        break;
      case 'trafficSpeed':
        _selectedTrafficSpeed = onchangedValue;
        break;
      //pole fields
      case 'poleNumber':
        _selectedPoleNumber = onchangedValue;
        _poleNumberController.text = onchangedValue;
        break;
      case 'poleTransFormerNo':
        _poleTransformerNoController.text = onchangedValue;
        break;
      case 'switchPointNo':
        _selectedSwitchPointNo = onchangedValue;
        // _switchPointController.text = onchangedValue;
        break;
      case 'escomPoleNumber':
        _selectedEscomPoleNumber = onchangedValue;
        break;
      case 'exCorpPoleNo':
        _exCorpPoleNoController.text = onchangedValue;
        break;
      case 'clampType':
        _selectedClampType = onchangedValue;
        break;
      case 'customInputLength':
        _selectedClampLength = onchangedValue;
        break;
      case 'customInputWidth':
        _selectedClampWidth = onchangedValue;
        break;
      case 'customInputUnits':
        _selectedClampUnits = onchangedValue;
        break;
      case 'assetType':
        _selectedAssetType = onchangedValue;
        break;
      case 'poleType':
        _selectedPoleType = onchangedValue;
        updatePoleTypeCascade(_selectedPoleType);
        break;
      case 'poleHeight':
        _selectedPoleHeight = onchangedValue;
        break;
      case 'poleSpan':
        _selectedPoleSpan = onchangedValue;
        break;
      case 'poleCondition':
        _selectedPoleCondition = onchangedValue;
        break;
      case 'armCount':
        _selectedArmCount = onchangedValue;
        _resetArmAndLampCounts(int.tryParse(_selectedArmCount) ?? 0);
        break;
      case 'armLength':
        _selectedArmLength = onchangedValue;
        break;
      case 'armCondition':
        _selectedArmCondition = onchangedValue;
        break;
      case 'connectionType':
        _selectedConnectionType = onchangedValue;
        break;
      case 'Bracket Required':
        _isBracketRequired = onchangedValue;
        break;
      case 'Earthing':
        _isEarthingRequired = onchangedValue;
        break;
      case 'Control Wire Status':
        _isControlWireStatus = onchangedValue;
        break;
      case 'manualSwitchCtrl':
        _selectedManualSwitchCtrl = onchangedValue;
        break;
      case 'Working':
      case 'Not Working':
        _updateLampStatusCounts(fieldName, onchangedValue);
        break;
      case 'lampType':
        _selectedLampType = onchangedValue;
        break;
      case 'lampWatts':
        _selectedLampWatts = onchangedValue;
        break;
      case 'comments':
        _selectedComments = onchangedValue;
        break;
      case 'manualEnteredLocation':
        _selectedManualEnteredLocation = onchangedValue;
        break;
      case 'incomingTransLine':
        _selectedIncomingTransLine = onchangedValue;
        break;
      case 'incomingTransType':
        _selectedIncomingTransType = onchangedValue;
        break;
      case 'bracketMountingHeight':
        _selectedBracketMountingHeight = onchangedValue;
        break;
      case 'customBracketHeight':
        _selectedCustomBracketHeight = onchangedValue;
        break;
      case 'Good':
      case 'Bad':
        _updateArmConditionCounts(fieldName, onchangedValue);
        break;
      case 'motorCondition':
        _selectedPoleMotorCondition = onchangedValue;
        break;
      case 'motorRating':
        _poleMotorRatingController.text = onchangedValue;
        break;
      case 'motorMake':
        _poleMotorMakeController.text = onchangedValue;
        break;
      case 'motorModel':
        _poleMotorModelController.text = onchangedValue;
        break;
      case 'winchCondition':
        _selectedPoleWinchCondition = onchangedValue;
        break;
      case 'ropeCondition':
        _selectedPoleRopeCondition = onchangedValue;
        break;
      case 'ratingUnits':
        _selectedPoleRatingUnits = onchangedValue;
        break;

      //Switch Point fields
      case 'switchPointNumber':
        _selectedSpNumber = onchangedValue;
        _switchPointNumberController.text = onchangedValue;
        break;
      case 'spCondition':
        _selectedSpCondition = onchangedValue;
        break;
      case 'rrNumber':
        _selectedSpRrNo = onchangedValue;
        break;
      case 'spTransFormerNo':
        _spTransformerNoController.text = onchangedValue;
        break;
      case 'spType':
        _selectedSwitchPointType = onchangedValue;
        break;
      case 'spId':
        _switchPointIdController.text = onchangedValue;
        _selectedSpId = onchangedValue;
        break;
      case 'spMeter':
        _selectedSPMeter = onchangedValue;
        break;
      case 'spConnectedLoad':
        _spConnectedLoadController.text = onchangedValue;
        _selectedSpConnectedLoad = onchangedValue;
        break;
      case 'spMeterNo':
        _spMeterNoController.text = onchangedValue;
        _selectedSpMeterNo = onchangedValue;
        break;
      case 'spMeterType':
        _selectedSpMeterType = onchangedValue;
        break;
      case 'spMake':
        _selectedSpMake = onchangedValue;
        break;
      case 'spPhase':
        _selectedSpPhase = onchangedValue;
        break;
      case 'spEarthingCondition':
        _selectedSpEarthingCondition = onchangedValue;
        break;

      //transformer fields
      case 'transNo':
        _transNoController.text = onchangedValue;
        break;
      case 'transCapacity':
        _transCapacityController.text = onchangedValue;
        break;
    }

    notifyListeners();
  }

  void updateRoadCategoryCascade(
      BuildContext context, r.WidgetRef ref, String newCategory) {
    _selectedRoadCategory = newCategory;

    // Set Road Type
    final roadTypeList = roadTypeOptions[newCategory] ?? [];
    _selectedRoadType = (roadTypeList.isNotEmpty ? roadTypeList.first : null)!;

    // Set Road Width
    final widthList = roadWidthOptions[_selectedRoadType] ?? [];
    _selectedRoadWidth = (widthList.isNotEmpty ? widthList.first : null)!;

    // Set Vehicle Access
    final accessList = vehicleAccessOptions[_selectedRoadWidth] ?? [];
    _selectedVehicleAccess = (accessList.isNotEmpty ? accessList.first : null)!;

    notifyListeners();
  }

  void updateRoadTypeCascade(
      BuildContext context, r.WidgetRef ref, String newType) {
    _selectedRoadType = newType;

    final widthList = roadWidthOptions[newType] ?? [];
    _selectedRoadWidth = (widthList.isNotEmpty ? widthList.first : null)!;

    final accessList = vehicleAccessOptions[_selectedRoadWidth] ?? [];
    _selectedVehicleAccess = (accessList.isNotEmpty ? accessList.first : null)!;

    notifyListeners();
  }

  void updateRoadWidthCascade(
      BuildContext context, r.WidgetRef ref, String newWidth) {
    _selectedRoadWidth = newWidth;

    // Update vehicle access list based on new width
    final accessList = vehicleAccessOptions[newWidth] ?? [];
    _selectedVehicleAccess = (accessList.isNotEmpty ? accessList.first : null)!;

    notifyListeners();
  }

  void updateVehicleAccess(String newAccess) {
    _selectedVehicleAccess = newAccess;
    notifyListeners();
  }

  void updatePoleHeightCascade(String roadType) {
    if (roadType == 'Park Road') {
      poleHeightOptions = ['3 m', '5 m'];
      _selectedPoleHeight = '3 m';
      bracketMountingHeightOptions = ['Others'];
      _selectedBracketMountingHeight = 'Others';
    }
  }

  void updatePoleTypeCascade(String type) {
    if (type == 'High Mast(HM)') {
      armCountOptions = ['6', '8', '12', '16', '20'];
      _selectedArmCount = '6';
      poleHeightOptions = ['20 m', 'Others'];
      _selectedPoleHeight = '20 m';
      lampWattageOptions.clear();
      lampWattageOptions = {
        'SVL': ['250 W', '400 W'],
        'MH': ['250 W', '400 W'],
        'FL LED': ['125 W', '150 W', '200 W'],
      };
      _selectedLampType = 'SVL';
      _selectedLampWatts = '250 W';
    } else if (type == 'Mini Mast(MM)') {
      armCountOptions = ['4', '6', '8'];
      poleHeightOptions = ['10 m', '12 m', '16 m', '20 m', 'Others'];
      _selectedArmCount = '4';
      _selectedPoleHeight = '10 m';
      lampWattageOptions.clear();
      lampWattageOptions = {
        'Tube light': ['40 W'],
        'SVL': ['250 W', '400 W'],
        'CFL': ['25 W', '36 W', '65 W', '72 W', '85 W'],
        'LED': [
          '20 W',
          '40 W',
          '60 W',
          '70 W',
          '90 W',
          '120 W',
          '150 W',
          '200 W'
        ],
        'T5': ['96 W', '120 W', '220 W'],
        'BULB': ['10 W'],
        'MH': ['250 W', '400 W']
      };
      _selectedLampType = 'Tube light';
      _selectedLampWatts = '40 W';
    } else {
      armCountOptions = ['0', '1', '2', '4', '8', '12'];
      _selectedArmCount = '1';
      poleHeightOptions = [
        '7 m',
        '8 m',
        '9 m',
        '10 m',
        '12 m',
        '16 m',
        '20 m',
        'Others'
      ];
      _selectedPoleHeight = '7 m';
      lampWattageOptions.clear();
      lampWattageOptions = {
        'Tube light': ['40 W'],
        'SVL': ['150 W', '250 W', '400 W'],
        'CFL': ['25 W', '36 W', '65 W', '72 W', '85 W'],
        'LED': [
          '20 W',
          '40 W',
          '60 W',
          '70 W',
          '90 W',
          '120 W',
          '150 W',
          '200 W'
        ],
        'T5': ['96 W', '120 W', '220 W'],
        'BULB': ['10 W'],
        'MH': ['150 W', '250 W', '400 W']
      };
      _selectedLampType = 'Tube light';
      _selectedLampWatts = '40 W';
    }
    _resetArmAndLampCounts(int.tryParse(_selectedArmCount) ?? 0);
  }

  void _resetArmAndLampCounts(int armCount) {
    _selectedGoodArmCount = '0';
    _selectedBadArmCount = '0';
    _selectedMissingArmCount = armCount.toString();
    _selectedWorkingCount = '0';
    _selectedNotWorkingCount = '0';
    _selectedMissingCount = armCount.toString();

    goodArmTextController.text = '0';
    badArmTextController.text = '0';
    missingArmTextController.text = armCount.toString();

    workingTextController.text = '0';
    notWorkingTextController.text = '0';
    missingTextController.text = armCount.toString();

    // Reset lamp list to have only one entry
    _selectedLamps = [
      {'type': _selectedLampType, 'watts': _selectedLampWatts}
    ];
  }

  void addLamp() {
    if (_selectedLamps.length < int.parse(_selectedArmCount)) {
      _selectedLamps.add({
        'type': _selectedLampType,
        'watts': _selectedLampWatts,
      });
      log('lamp profiles :$_selectedLamps');
      notifyListeners();
    }
  }

  void updateLampType(int index, String type) {
    if (index < _selectedLamps.length) {
      final wattsList = lampWattageOptions[type] ?? [];
      final watts = wattsList.isNotEmpty ? wattsList.first : '40 W';
      _selectedLamps[index] = {
        'type': type,
        'watts': watts,
      };
      log('lamp profiles :$_selectedLamps');
      notifyListeners();
    }
  }

  void updateLampWatts(int index, String watts) {
    if (index < _selectedLamps.length) {
      _selectedLamps[index]['watts'] = watts;
      log('lamp profiles :$_selectedLamps');
      notifyListeners();
    }
  }

  void _updateArmConditionCounts(String field, String value) {
    if (field == 'Good') {
      _selectedGoodArmCount = value;
    } else if (field == 'Bad') {
      _selectedBadArmCount = value;
    }

    int armCount = int.tryParse(_selectedArmCount) ?? 0;
    int good = int.tryParse(_selectedGoodArmCount) ?? 0;
    int bad = int.tryParse(_selectedBadArmCount) ?? 0;

    if (good + bad > armCount) {
      if (field == 'Good') {
        good = armCount - bad;
        _selectedGoodArmCount = good.toString();
        goodArmTextController.text = _selectedGoodArmCount;
      } else {
        bad = armCount - good;
        _selectedBadArmCount = bad.toString();
        badArmTextController.text = _selectedBadArmCount;
      }
    }

    int missing = armCount - good - bad;
    _selectedMissingArmCount = missing.toString();
    missingArmTextController.text = _selectedMissingArmCount;
  }

  void _updateLampStatusCounts(String field, String value) {
    if (field == 'Working') {
      _selectedWorkingCount = value;
    } else if (field == 'Not Working') {
      _selectedNotWorkingCount = value;
    }

    int armCount = int.tryParse(_selectedArmCount) ?? 0;
    int working = int.tryParse(_selectedWorkingCount) ?? 0;
    int notWorking = int.tryParse(_selectedNotWorkingCount) ?? 0;

    if (working + notWorking > armCount) {
      if (field == 'Working') {
        working = armCount - notWorking;
        _selectedWorkingCount = working.toString();
        workingTextController.text = _selectedWorkingCount;
      } else {
        notWorking = armCount - working;
        _selectedNotWorkingCount = notWorking.toString();
        notWorkingTextController.text = _selectedNotWorkingCount;
      }
    }

    int missing = armCount - working - notWorking;
    _selectedMissingCount = missing.toString();
    missingTextController.text = _selectedMissingCount;
  }

  clearAllUpdatedSelectedFields() {
    //pole details
    _selectedEscomPoleNumber = '';
    _selectedPoleNumber = '';
    poleNumberController.clear();
    workingTextController.text = '0';
    notWorkingTextController.text = '0';
    missingTextController.text = '1';
    goodArmTextController.text = '0';
    badArmTextController.text = '0';
    missingArmTextController.text = '1';
    commentsController.clear();
    _surveyManualLocationEntry.clear();
    _fetchedLandMark = '';
    _fetchedLat = '';
    _fetchedLong = '';
    _fetchedAltitude = '';
    _selectedPoleSpan = '';
    _selectedPoleType = 'RCC';
    _selectedPoleCondition = 'Good';
    _selectedPoleHeight = '7 m';
    _selectedConnectionType = 'Connection Type';
    _selectedArmCount = '1';
    _selectedArmCondition = 'Good';
    _selectedArmLength = 'Normal';
    _selectedRoadCategory = 'A1';
    _selectedRoadWidth = 'above 9m';
    _selectedVehicleAccess = 'Long Truck';
    _selectedIncomingTransLine = 'Under Ground';
    _selectedIncomingTransType = 'Dedicated for Streetlight';
    _selectedClampType = '6 x 3 inch';
    _selectedClampLength = '';
    _selectedClampWidth = '';
    _selectedClampUnits = '';
    _isEarthingRequired = false;
    _isBracketRequired = false;
    _isControlWireStatus = false;
    _selectedManualSwitchCtrl = 'Exists - Good Condition';
    _selectedWorkingCount = '0';
    _selectedNotWorkingCount = '0';
    _selectedMissingCount = '0';
    _selectedComments = '';
    _selectedManualEnteredLocation = '';
    _selectedBracketMountingHeight = '6 m';
    bracketMountingHeightOptions = [
      'Not Required',
      '6 m',
      '7 m',
      '8 m',
      '9 m',
      '12 m',
      '16 m',
      'Others'
    ];
    _selectedLampType = 'Tube light';
    _selectedLampWatts = '40 W';
    _selectedLamps = [
      {'type': 'Tube light', 'watts': '40 W'}
    ];
    _exCorpPoleNoController.clear();
    _selectedPoleMotorCondition = 'Good';
    _poleMotorMakeController.clear();
    _poleMotorModelController.clear();
    _poleMotorRatingController.clear();
    _selectedPoleRatingUnits = 'hp';
    _selectedPoleWinchCondition = 'Good';
    _selectedPoleRopeCondition = 'Working';
    lampWattageOptions = {
      'Tube light': ['40 W'],
      'SVL': ['150 W', '250 W', '400 W'],
      'CFL': ['25 W', '36 W', '65 W', '72 W', '85 W'],
      'LED': [
        '20 W',
        '40 W',
        '60 W',
        '70 W',
        '90 W',
        '120 W',
        '150 W',
        '200 W'
      ],
      'T5': ['96 W', '120 W', '220 W'],
      'BULB': ['10 W'],
      'MH': ['150 W', '250 W', '400 W']
    };
    armCountOptions = ['0', '1', '2', '4', '8', '12'];
    poleHeightOptions = [
      '7 m',
      '8 m',
      '9 m',
      '10 m',
      '12 m',
      '16 m',
      '20 m',
      'Others'
    ];
    _selectedTrafficDensity = 'High';
    _selectedTrafficSpeed = 'Slow Moving';
    _poleTransformerNoController.clear();
// switch point details
    _switchPointNumberController.clear();
    _selectedSpNumber = '';
    _selectedSpRrNo = '';
    _rrNumberController.clear();
    _switchPointIdController.clear();
    _selectedSpId = '';
    _spConnectedLoadController.clear();
    _selectedSpConnectedLoad = '';
    _spMeterNoController.clear();
    _selectedSpMeterNo = '';
    _selectedSpMake = 'L&T';
    _spMakeController.text = 'L&T';
    _selectedSPMeter = 'Working';
    _selectedSpMeterType = 'Digital';
    _selectedSpPhase = '1';
    _selectedSpCondition = 'Working';
    _selectedSpEarthingCondition = 'Good';
    _spTransformerNoController.clear();

//transformer details
    _transNoController.clear();
    _transCapacityController.clear();
// base survey details
    _selectedRoadType = 'Main Road';
    _selectedRoadCategory = 'A1';
    _selectedRoadWidth = 'above 9m';
    _selectedVehicleAccess = 'Long Truck';
    _selectedAssetType = 'Switch Point';
  }

  Future<bool> isValidPoleNumber(String poleNumber) async {
    final trimmed = poleNumber.trim();

    final regex = RegExp(r'^BBMPSNLW\d{1,2}P\d{1,4}$', caseSensitive: true);

    return regex.hasMatch(trimmed);
  }

  Future<bool> isValidSwitchPointNo(String poleNumber) async {
    final trimmed = poleNumber.trim();

    final regex = RegExp(r'^\d{1,3}$', caseSensitive: true);

    return regex.hasMatch(trimmed);
  }

  Future<void> insertPoleSurveyDetails(context, ref) async {
    final dataBloc = Provider.of<DataModel>(context, listen: false);
    int intClampTypeLength = 0;
    int intClampTypeWidth = 0;
    // int intCustomBracketHeight = 0;
    if (_selectedClampType != 'Clamp Type Not Required') {
      if (_selectedClampType == 'Others') {
        intClampTypeLength = int.parse(_selectedClampLength);
        intClampTypeWidth = int.parse(_selectedClampWidth);
      } else {
        List<String> splittedClampType = _selectedClampType.split(' ');
        intClampTypeLength = int.parse(splittedClampType[0]);
        intClampTypeWidth = int.parse(splittedClampType[2]);
      }
    }

    if (_selectedBracketMountingHeight != 'Not Required') {
      if (_selectedBracketMountingHeight == 'Others') {
        _selectedBracketMountingHeight = '$_selectedCustomBracketHeight m';
      }
    }

    if (_selectedPoleType == 'High Mast(HM)' ||
        _selectedPoleType == 'Mini Mast(MM)') {
      // _selectedPoleSpan = '';
      _selectedCustomBracketHeight = '';
      _selectedBracketMountingHeight = '';
      _selectedClampType = '';
      intClampTypeLength = 0;
      intClampTypeWidth = 0;
    }

    if ((_selectedWorkingCount == '0' || _selectedWorkingCount == '') &&
        (_selectedNotWorkingCount == '0' || _selectedNotWorkingCount == '')) {
      _selectedLamps = [];
    }
    var box = await Hive.openBox('surveyBox');
    log('Main thread: Opening surveyBox for pole survey insertion');
    log('Main thread: Current surveyBox length before insertion: ${box.length}');

    _surveyDetails = PoleSurveyModel(
        roadType: _selectedRoadType,
        trafficDensity: _selectedTrafficDensity,
        trafficSpeed: _selectedTrafficSpeed,
        assetType: _selectedAssetType,
        latitude: double.parse(_fetchedLat == '' ? '0' : _fetchedLat),
        longitude: double.parse(_fetchedLong == '' ? '0' : _fetchedLong),
        accuracy: _fetchedAcc,
        altitude: double.parse(_fetchedAltitude == '' ? '0' : _fetchedAltitude),
        landmark: fetchedLandMark,
        poleType: _selectedPoleType,
        poleHeight: _selectedPoleHeight,
        poleSpan: _selectedPoleSpan,
        poleCondition: _selectedPoleCondition,
        goodArmCount: _selectedGoodArmCount,
        badArmCount: _selectedBadArmCount,
        missingArmCount: _selectedMissingArmCount,
        armCount: _selectedArmCount,
        armLength: _selectedArmLength,
        bracketRequired: _isBracketRequired,
        earthingRequired: _isEarthingRequired,
        controlWireStatus: _isControlWireStatus,
        manualSwitchControl: _selectedManualSwitchCtrl,
        workingCount: _selectedWorkingCount,
        notWorkingCount: _selectedNotWorkingCount,
        missingCount: _selectedMissingCount,
        poleNumber: _selectedPoleNumber,
        switchPointNo: _selectedSwitchPointNo,
        poleTransformerNo: poleTransformerNoController.text,
        exCorpPoleNo: _exCorpPoleNoController.text,
        escomPoleNumber: _selectedEscomPoleNumber,
        // lampType: _selectedLampType,
        // lampWatts: _selectedLampWatts,
        selectedLamps: _selectedLamps,
        comments: _selectedComments,
        uuidFileName1: _imageDetails.fileName1,
        uuidFileName2: _imageDetails.fileName2,
        uuidFileName3: _imageDetails.fileName3,
        installedOn: DateTime.now().millisecondsSinceEpoch.toString(),
        installedBy: dataBloc.userName,
        region: dataBloc.region,
        zone: dataBloc.zone,
        ward: dataBloc.ward,
        wardId: dataBloc.wardId,
        customerId: dataBloc.customerId,
        manualEnteredLocation: _selectedManualEnteredLocation,
        roadCategory: _selectedRoadCategory,
        roadWidth: _selectedRoadWidth,
        vehicleAccess: _selectedVehicleAccess,
        incomingTransLine: _selectedIncomingTransLine,
        incomingTransType: _selectedIncomingTransType,
        clampType: _selectedClampType,
        clampTypeLength: intClampTypeLength,
        clampTypeWidth: intClampTypeWidth,
        clampTypeUnits: _selectedClampUnits,
        bracketMountingHeight: _selectedBracketMountingHeight,
        carrierName: _carrierName,
        signalStrengthLevel: _signalStrengthLevel,
        motorCondition: _selectedPoleMotorCondition,
        motorRating: poleMotorRatingController.text,
        motorMake: poleMotorMakeController.text,
        motorModel: poleMotorModelController.text,
        winchCondition: selectedPoleWinchCondition,
        ropeCondition: selectedPoleRopeCondition);
// Save the model as a Map
    final addedKey = await box.add(_surveyDetails.toMap());
    log('Main thread: Pole survey data saved to Hive with key: $addedKey');
    log('Main thread: surveyBox length after insertion: ${box.length}');
    log('Main thread: surveyBox keys: ${box.keys.toList()}');
    log('survey details ${_surveyDetails.toMap()}');

    // CRITICAL: Force Hive to write to disk immediately
    await box.flush();
    log('Main thread: Forced Hive flush to disk');

    clearAllUpdatedSelectedFields();

    // Ensure Hive write is committed and box is properly closed before proceeding
    await box.close();
    log('Main thread: surveyBox closed after insertion');

    // Verify that Hive files are actually created
    try {
      final appDocDir = await getApplicationDocumentsDirectory();
      final dir = Directory(appDocDir.path);
      final files =
          dir.listSync().where((f) => f.path.contains('hive')).toList();
      log('Main thread: Hive files after survey save: ${files.map((f) => f.path.split('/').last).toList()}');

      // Also check if we can reopen the box and verify the data
      final verifyBox = await Hive.openBox('surveyBox');
      log('Main thread: Verification - surveyBox length: ${verifyBox.length}');
      log('Main thread: Verification - surveyBox keys: ${verifyBox.keys.toList()}');
      if (verifyBox.length > 0) {
        final lastRecord = verifyBox.getAt(verifyBox.length - 1);
        if (lastRecord is Map) {
          log('Main thread: Verification - last record assetType: ${lastRecord['assetType']}');
        }
      }
      await verifyBox.close();
    } catch (e) {
      log('Main thread: Could not verify Hive files: $e');
    }

    await Future.delayed(const Duration(milliseconds: 500));

    await alertPopUp(
        context, 'Data Updated Successfully', 'assets/animation/tick.json');
    Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const BaseSurveyInformation()),
        (Route<dynamic> route) => false);
  }

  Future<void> insertSpSurveyDetails(context, ref) async {
    final dataBloc = Provider.of<DataModel>(context, listen: false);
    var box = await Hive.openBox('surveyBox');

    _spSurveyDetails = SpSurveyModel(
        roadType: _selectedRoadType,
        trafficDensity: _selectedTrafficDensity,
        trafficSpeed: _selectedTrafficSpeed,
        assetType: _selectedAssetType,
        latitude: double.parse(_fetchedLat == '' ? '0' : _fetchedLat),
        longitude: double.parse(_fetchedLong == '' ? '0' : _fetchedLong),
        accuracy: _fetchedAcc,
        altitude: double.parse(_fetchedAltitude == '' ? '0' : _fetchedAltitude),
        landmark: fetchedLandMark,
        spNo: _selectedSpNumber,
        spType: _selectedSwitchPointType,
        rrNo: _selectedSpRrNo,
        spTransformerNo: spTransformerNoController.text,
        spId: _selectedSpId,
        spMeter: _selectedSPMeter,
        spconnectedLoad: _selectedSpConnectedLoad,
        spMeterMake: _selectedSpMake,
        spMeterPhase: _selectedSpPhase,
        spMeterType: _selectedSpMeterType,
        spMeterNo: _selectedSpMeterNo,
        spCondition: _selectedSpCondition,
        spEarthingCondition: _selectedSpEarthingCondition,
        uuidFileName1: _imageDetails.fileName1,
        uuidFileName2: _imageDetails.fileName2,
        uuidFileName3: _imageDetails.fileName3,
        installedOn: DateTime.now().millisecondsSinceEpoch.toString(),
        installedBy: dataBloc.userName,
        region: dataBloc.region,
        zone: dataBloc.zone,
        ward: dataBloc.ward,
        wardId: dataBloc.wardId,
        customerId: dataBloc.customerId,
        manualEnteredLocation: _selectedManualEnteredLocation,
        roadCategory: _selectedRoadCategory,
        roadWidth: _selectedRoadWidth,
        vehicleAccess: _selectedVehicleAccess,
        comments: _selectedComments,
        carrierName: _carrierName,
        signalStrengthLevel: _signalStrengthLevel);
// Save the model as a Map
    await box.add(_spSurveyDetails.toMap());
    log('survey details ${_spSurveyDetails.toMap()}');

    // CRITICAL: Force Hive to write to disk immediately
    await box.flush();
    await box.close();
    log('Main thread: SP survey data flushed and box closed');

    clearAllUpdatedSelectedFields();
    await alertPopUp(
        context, 'Data Updated Successfully', 'assets/animation/tick.json');
    Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const BaseSurveyInformation()),
        (Route<dynamic> route) => false);
  }

  Future<void> insertTransSurveyDetails(context, ref) async {
    final dataBloc = Provider.of<DataModel>(context, listen: false);
    var box = await Hive.openBox('surveyBox');

    _transSurveyDetails = TransformerSurveyModel(
        roadType: _selectedRoadType,
        trafficDensity: _selectedTrafficDensity,
        trafficSpeed: _selectedTrafficSpeed,
        assetType: _selectedAssetType,
        latitude: double.parse(_fetchedLat == '' ? '0' : _fetchedLat),
        longitude: double.parse(_fetchedLong == '' ? '0' : _fetchedLong),
        accuracy: _fetchedAcc,
        altitude: double.parse(_fetchedAltitude == '' ? '0' : _fetchedAltitude),
        landmark: fetchedLandMark,
        uuidFileName1: _imageDetails.fileName1,
        uuidFileName2: _imageDetails.fileName2,
        uuidFileName3: _imageDetails.fileName3,
        installedOn: DateTime.now().millisecondsSinceEpoch.toString(),
        installedBy: dataBloc.userName,
        region: dataBloc.region,
        zone: dataBloc.zone,
        ward: dataBloc.ward,
        wardId: dataBloc.wardId,
        customerId: dataBloc.customerId,
        manualEnteredLocation: _selectedManualEnteredLocation,
        roadCategory: _selectedRoadCategory,
        roadWidth: _selectedRoadWidth,
        vehicleAccess: _selectedVehicleAccess,
        comments: _selectedComments,
        carrierName: _carrierName,
        signalStrengthLevel: _signalStrengthLevel,
        transformerNo: _transNoController.text,
        transCapacity: _transCapacityController.text);
// Save the model as a Map
    await box.add(_transSurveyDetails.toMap());
    log('survey details ${_transSurveyDetails.toMap()}');

    // CRITICAL: Force Hive to write to disk immediately
    await box.flush();
    await box.close();
    log('Main thread: Transformer survey data flushed and box closed');

    clearAllUpdatedSelectedFields();
    await alertPopUp(
        context, 'Data Updated Successfully', 'assets/animation/tick.json');
    Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const BaseSurveyInformation()),
        (Route<dynamic> route) => false);
  }

  Future<void> insertCapturedImages(BuildContext context, r.WidgetRef ref,
      List<XFile?> capturedImages) async {
    final box = await Hive.openBox('imagesBox');
    var uuid = const Uuid();
    try {
      String? base64Image1 = '', base64Image2 = '', base64Image3 = '';
      String? fileName1 = '', fileName2 = '', fileName3 = '';

      if (capturedImages[0] != null) {
        base64Image1 =
            base64Encode(await File(capturedImages[0]!.path).readAsBytes());
        final fileName = uuid.v1();
        fileName1 = '$fileName.jpg';
      }

      if (capturedImages[1] != null) {
        base64Image2 =
            base64Encode(await File(capturedImages[1]!.path).readAsBytes());
        final fileName = uuid.v1();

        fileName2 = '$fileName.jpg';
      }

      if (capturedImages[2] != null) {
        base64Image3 =
            base64Encode(await File(capturedImages[2]!.path).readAsBytes());
        final fileName = uuid.v1();
        fileName3 = '$fileName.jpg';
      }

      _imageDetails = MultiCapturedImageModel(
        fileName1: fileName1,
        base64Image1: base64Image1,
        fileName2: fileName2,
        base64Image2: base64Image2,
        fileName3: fileName3,
        base64Image3: base64Image3,
      );

      await box.add(_imageDetails.toMap());

      log("Stored image filenames: ${_imageDetails.fileName1}, ${_imageDetails.fileName2}, ${_imageDetails.fileName3}");
      log("Image details: ${_imageDetails.toMap()}");

      // CRITICAL: Force Hive to write to disk immediately
      await box.flush();
      log('Main thread: Forced imagesBox flush to disk');

      // Close the images box to prevent concurrent access issues
      await box.close();
      log('Main thread: imagesBox closed after insertion');
    } catch (e) {
      log('Error while saving images: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Error saving images.')),
      );
    }
  }

  onClickEventIsFor(val) {
    _clickEventIsFor = val;
    notifyListeners();
  }

  checkPermission() async {
    bool serviceEnabled;
    LocationPermission serviceEnb;
    serviceEnb = await Geolocator.checkPermission();
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    switch (serviceEnb) {
      case LocationPermission.denied:
        serviceEnb = await Geolocator.requestPermission();
        break;
      case LocationPermission.deniedForever:
        await appsettings.openAppSettings();
        break;
      case LocationPermission.whileInUse:
        null;
        break;
      case LocationPermission.always:
        null;
        break;
      case LocationPermission.unableToDetermine:
        null;
        break;
    }

    if (serviceEnb == LocationPermission.whileInUse ||
        serviceEnb == LocationPermission.always) {
      if (serviceEnabled) {
        fetchLocationForSurvey(); //modified
      }
    }
  }

  Future<String> getUserLocation(lat, long) async {
    List<Placemark> placemarks = await placemarkFromCoordinates(lat, long);
    Placemark place = placemarks[0];
    // Create a list to hold non-empty location components
    List<String> locationComponents = [];

    // Add non-empty components to the list
    if (place.subLocality != null && place.subLocality!.isNotEmpty) {
      locationComponents.add(place.subLocality!);
    }
    if (place.locality != null && place.locality!.isNotEmpty) {
      locationComponents.add(place.locality!);
    }
    if (place.thoroughfare != null && place.thoroughfare!.isNotEmpty) {
      locationComponents.add(place.thoroughfare!);
    }
    if (place.administrativeArea != null &&
        place.administrativeArea!.isNotEmpty) {
      locationComponents.add(place.administrativeArea!);
    }
    if (place.country != null && place.country!.isNotEmpty) {
      locationComponents.add(place.country!);
    }
    if (place.postalCode != null && place.postalCode!.isNotEmpty) {
      locationComponents.add(place.postalCode!);
    }

    // Join the non-empty components into a single string
    return locationComponents.join(', ');
  }

  fetchLocationForSurvey() async {
    bool serviceEnabled;
    Position locationData;
    serviceEnabled =
        await GeolocatorPlatform.instance.isLocationServiceEnabled();
    LocationPermission serviceEnb = await Geolocator.checkPermission();
    if (serviceEnb == LocationPermission.denied ||
        serviceEnb == LocationPermission.deniedForever) {
      checkPermission();
    }

    locationData = await Geolocator.getCurrentPosition(
        locationSettings: AndroidSettings(accuracy: LocationAccuracy.high));
    _fetchedAltitude = locationData.altitude.toStringAsFixed(4);
    _fetchedAcc = locationData.accuracy.toStringAsFixed(4);
    _fetchedLat = locationData.latitude.toString();
    _fetchedLong = locationData.longitude.toString();
    await Utility.isConnected().then((value) async {
      if (value) {
        _fetchedLandMark = await getUserLocation(
            double.parse(_fetchedLat), double.parse(_fetchedLong));
      } else {
        null;
      }
    });
    _isSurveyLocationFetched = true;
    notifyListeners();
    log("OUTPUT , fetchedAcc:${_fetchedAcc.toString()}, fetchedLat:${_fetchedLat.toString()}, fetchedLong:${_fetchedLong.toString()}");
    return [
      _fetchedAcc.toString(),
      _fetchedLat.toString(),
      _fetchedLong.toString(),
      _fetchedAltitude.toString(),
    ];
  }

  Future<void> fetchNetworkDetails() async {
    var connectivityResult = await (Connectivity().checkConnectivity());
    await Permission.locationWhenInUse.status;
    await ensurePermission(Permission.phone);

    if (connectivityResult.contains(ConnectivityResult.wifi)) {
      _carrierName = 'wifi/Others';
      int? signalStrength =
          await FlutterInternetSignal().getWifiSignalStrength();
      _signalStrengthLevel = mapDbmToLevel(signalStrength ?? 0);

      log("Connected to Wifi");
      log("Carrier: $_carrierName");
      log("Mobile Signal Strength: $_signalStrengthLevel / 5");
    } else if (connectivityResult.contains(ConnectivityResult.mobile)) {
      final carrierInfo = await CarrierInfo.getAndroidInfo();
      TelephonyInfo? activeSim;
      if (carrierInfo?.telephonyInfo != null) {
        try {
          activeSim = carrierInfo!.telephonyInfo.firstWhere(
            (sim) => sim.simState == 'SIM_STATE_READY',
          );
          _carrierName = activeSim.carrierName;
          log('carrierName : $_carrierName');
        } catch (e) {
          activeSim = null; // if no ready SIM
          _carrierName = activeSim?.carrierName ?? 'Unknown';
        }
      }

      int? signalStrength =
          await FlutterInternetSignal().getMobileSignalStrength();
      _signalStrengthLevel = mapDbmToLevel(signalStrength ?? 0);

      log("Connected to Mobile Data");
      log('telephony details : $carrierInfo');
      log("Mobile Signal Strength: $_signalStrengthLevel / 5");
    } else {
      log("No Internet Connection");
      _carrierName = 'offline';
      _signalStrengthLevel = 0;
    }
    notifyListeners();
  }

  int mapDbmToLevel(int dbm) {
    if (dbm >= -85) return 5;
    if (dbm >= -95) return 4;
    if (dbm >= -105) return 3;
    if (dbm >= -115) return 2;
    if (dbm >= -125) return 1;
    return 0;
  }

  Future<void> ensurePermission(Permission permission) async {
    while (true) {
      final status = await Permission.phone.request();

      if (status.isGranted) {
        break;
      } else if (status.isPermanentlyDenied) {
        log('${permission.toString()} permanently denied');
        // You can open app settings here if you want
        await openAppSettings();
        break;
      } else {
        log('${permission.toString()} denied, asking again...');
      }
    }
  }

  String generatePoleName({
    required String template,
    required String wardNo,
    required String poleNo,
    required String switchPointNo,
  }) {
    String result = template;
    // BBMP-SNL-<wardNo>-<poleNo>-<switchPoint/PanelNo>
    // Replace switchPoint first, and remove hyphen if it's empty
    if (switchPointNo.isEmpty) {
      result = result.replaceAll(RegExp(r'-?<switchPoint/PanelNo>'), '');
    } else {
      result = result.replaceAll('<switchPoint/PanelNo>', switchPointNo);
    }
    result =
        result.replaceAll('<wardNo>', wardNo).replaceAll('<poleNo>', poleNo);

    return result;
  }

  String extractPolePrefix(String template, String wardNumber) {
    final match = RegExp(r'^(.*?<wardNo>)').firstMatch(template);
    if (match != null) {
      final prefixTemplate = match.group(1)!; // e.g. BBMP-SNL-<wardNo>
      return '${prefixTemplate.replaceAll('<wardNo>', wardNumber)}-P';
    }
    return '';
  }

  String get clickEventIsFor => _clickEventIsFor;
  String get selectedRoadType => _selectedRoadType;
  String get selectedAssetType => _selectedAssetType;
  String get selectedPoleType => _selectedPoleType;
  String get selectedPoleHeight => _selectedPoleHeight;
  String get selectedPoleSpan => _selectedPoleSpan;
  String get selectedPoleCondition => _selectedPoleCondition;
  String get selectedConnectionType => _selectedConnectionType;
  String get selectedArmCondition => _selectedArmCondition;
  String get selectedArmCount => _selectedArmCount;
  String get selectedArmLength => _selectedArmLength;
  String get selectedRoadCategory => _selectedRoadCategory;
  String get selectedRoadWidth => _selectedRoadWidth;
  String get selectedVehicleAccess => _selectedVehicleAccess;
  String get selectedIncomingTransType => _selectedIncomingTransType;
  String get selectedIncomingTransLine => _selectedIncomingTransLine;
  String get selectedLampType => _selectedLampType;
  String get selectedLampWatts => _selectedLampWatts;
  List<Map<String, String>> get selectedLamps => _selectedLamps;
  // bool get isClampRequired => _isClampRequired;
  bool get isBracketRequired => _isBracketRequired;
  bool get isEarthingRequired => _isEarthingRequired;
  bool get isControlWireStatus => _isControlWireStatus;
  String get selectedManualSwitchCtrl => _selectedManualSwitchCtrl;
  String get fetchedAcc => _fetchedAcc;
  String get fetchedAltitude => _fetchedAltitude;
  String get fetchedLandMark => _fetchedLandMark;
  String get fetchedLat => _fetchedLat;
  String get fetchedLong => _fetchedLong;
  String get selectedTrafficSpeed => _selectedTrafficSpeed;
  String get selectedTrafficDensity => _selectedTrafficDensity;
  String get selectedSwitchPointNo => _selectedSwitchPointNo;
  String get selectedBracketMountingHeight => _selectedBracketMountingHeight;
  String get selectedCustomBracketHeight => _selectedCustomBracketHeight;
  String get selectedClampType => _selectedClampType;
  String get selectedClampLength => _selectedClampLength;
  String get selectedClampWidth => _selectedClampWidth;
  String get selectedClampUnits => _selectedClampUnits;
  TextEditingController get poleMotorMakeController => _poleMotorMakeController;
  TextEditingController get spTransformerNoController =>
      _spTransformerNoController;
  TextEditingController get poleTransformerNoController =>
      _poleTransformerNoController;
  TextEditingController get poleMotorRatingController =>
      _poleMotorRatingController;
  TextEditingController get poleMotorModelController =>
      _poleMotorModelController;
  String get selectedPoleMotorCondition => _selectedPoleMotorCondition;
  String get selectedPoleWinchCondition => _selectedPoleWinchCondition;
  String get selectedPoleRopeCondition => _selectedPoleRopeCondition;
  String get selectedPoleRatingUnits => _selectedPoleRatingUnits;
  bool get isSurveyLocationFetched => _isSurveyLocationFetched;
  TextEditingController get workingTextController => _workingTextController;
  TextEditingController get goodArmTextController => _goodArmTextController;
  TextEditingController get badArmTextController => _badArmTextController;
  TextEditingController get missingArmTextController =>
      _missingArmTextController;
  TextEditingController get clampTypeLengthController =>
      _clampTypeLengthController;
  TextEditingController get clampTypeWidthController =>
      _clampTypeWidthController;

  TextEditingController get notWorkingTextController =>
      _notWorkingTextController;
  TextEditingController get missingTextController => _missingTextController;
  TextEditingController get poleNumberController => _poleNumberController;
  TextEditingController get exCorpPoleNoController => _exCorpPoleNoController;
  TextEditingController get switchPointController => _switchPointController;
  TextEditingController get commentsController => _commentsController;
  int get signalStrengthLevel => _signalStrengthLevel;
  String get carrierName => _carrierName;
  TextEditingController get surveyManualLocationEntry =>
      _surveyManualLocationEntry;

  String get selectedSwitchPointType => _selectedSwitchPointType;
  String get selectedSPMeter => _selectedSPMeter;
  String get selectedSpEarthingCondition => _selectedSpEarthingCondition;
  String get selectedSpCondition => _selectedSpCondition;
  String get selectedSpMeterType => _selectedSpMeterType;
  String get selectedSpPhase => _selectedSpPhase;
  String get selectedSpMake => _selectedSpMake;
  TextEditingController get switchPointNumberController =>
      _switchPointNumberController;
  TextEditingController get rrNumberController => _rrNumberController;
  TextEditingController get spMeterNoController => _spMeterNoController;
  TextEditingController get spMakeController => _spMakeController;
  TextEditingController get spConnectedLoadController =>
      _spConnectedLoadController;
  TextEditingController get switchPointIdController => _switchPointIdController;

  TextEditingController get transNoController => _transNoController;
  TextEditingController get transCapacityController => _transCapacityController;
}
