import 'dart:async';
import 'dart:developer';
import 'package:animated_hint_textfield/animated_hint_textfield.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as r;
import 'package:provider/provider.dart';
import 'package:schnell_pole_installation/Provider/provider.dart';
import 'package:schnell_pole_installation/survey/excel_export.dart';
import 'package:schnell_pole_installation/survey/survey_controller.dart';
import 'package:schnell_pole_installation/survey/background_sync_manager.dart';
import 'package:schnell_pole_installation/utils/box_container.dart';
import 'package:schnell_pole_installation/utils/constants.dart';
import 'package:schnell_pole_installation/utils/dialog_box.dart';
import 'package:schnell_pole_installation/utils/loader.dart';
import 'survey_take_photo.dart';

class TransformerSurvey extends r.ConsumerStatefulWidget {
  const TransformerSurvey({super.key});
  @override
  r.ConsumerState<TransformerSurvey> createState() => _TransformerSurvey();
}

class _TransformerSurvey extends r.ConsumerState<TransformerSurvey>
    with WidgetsBindingObserver {
  // late List<ConnectivityResult> connectivityResult;
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;
  List<ConnectivityResult> _connectionStatus = [ConnectivityResult.none];
  final Connectivity _connectivity = Connectivity();
  bool hasInternet = false;
  // Background sync manager
  final BackgroundSyncManager _syncManager = BackgroundSyncManager.instance;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(surveyController).fetchLocationForSurvey();
      // Check initial connectivity status
      _checkInitialConnectivity();
      // Initialize background sync manager
      // _initializeBackgroundSync();
    });
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }

  /// Initialize background sync manager
  Future<void> _initializeBackgroundSync() async {
    try {
      await _syncManager.initialize();
      log('Background sync manager initialized for transformer survey');
    } catch (e) {
      log('Failed to initialize background sync manager: $e');
    }
  }

  /// Check initial connectivity status when screen loads
  Future<void> _checkInitialConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      if (mounted) {
        await _updateConnectionStatus(result);
      }
    } catch (e) {
      log('Error checking initial connectivity: $e');
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // App came back to foreground, check connectivity
      log('App resumed - checking connectivity status');
      _checkConnectivityOnResume();
    }
  }

  /// Check connectivity when app resumes or screen comes back into focus
  Future<void> _checkConnectivityOnResume() async {
    try {
      final result = await _connectivity.checkConnectivity();
      if (mounted) {
        await _updateConnectionStatus(result);
      }
    } catch (e) {
      log('Error checking connectivity on resume: $e');
    }
  }

  Future<void> initConnectivity() async {
    late List<ConnectivityResult> result;
    // Platform messages may fail, so we use a try/catch PlatformException.
    try {
      result = await _connectivity.checkConnectivity();
      debugPrint('Connection Status : $result');
    } on PlatformException catch (e) {
      debugPrint(
        'Couldn\'t check connectivity status : $e',
      );
      return;
    }
    if (!mounted) {
      return Future.value(null);
    }
    return _updateConnectionStatus(result);
  }

  // Debounce mechanism for connectivity changes
  static DateTime? _lastConnectivityChange;
  static const Duration _connectivityDebounceDelay = Duration(seconds: 3);

  Future<void> _updateConnectionStatus(List<ConnectivityResult> result) async {
    if (!mounted) return;

    var dataBloc = Provider.of<DataModel>(context, listen: false);
    setState(() {
      _connectionStatus = result;
    });
    debugPrint('Update Install Connection Status : $_connectionStatus');

    if (_connectionStatus.contains(ConnectivityResult.none) ||
        _connectionStatus.isEmpty) {
      if (mounted) {
        setState(() {
          hasInternet = false;
        });
      }
      dataBloc.hasInternet = false;
      log('Internet connection lost');
    } else {
      // Debounce connectivity changes to prevent rapid upload attempts
      final now = DateTime.now();
      if (_lastConnectivityChange != null &&
          now.difference(_lastConnectivityChange!) <
              _connectivityDebounceDelay) {
        log('Connectivity change debounced - too soon since last change');
        if (mounted) {
          setState(() {
            hasInternet = true;
          });
        }
        dataBloc.hasInternet = true;
        return;
      }
      _lastConnectivityChange = now;

      log('Internet connection restored - triggering background sync');

      // Trigger background sync when internet is restored
      // _syncManager.startBackgroundSync();

      if (mounted) {
        setState(() {
          hasInternet = true;
        });
      }
      dataBloc.hasInternet = true;
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _connectivitySubscription.cancel();
    // Background sync manager will continue running in background
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double deviceWidth = MediaQuery.of(context).size.width;
    var dataBloc = Provider.of<DataModel>(context, listen: false);
    final TextEditingController surveyManualLocationController =
        ref.watch(surveyController).surveyManualLocationEntry;
    bool isSurveyLocationFetched =
        ref.watch(surveyController).isSurveyLocationFetched;
    final TextEditingController commentsController =
        ref.watch(surveyController).commentsController;
    String fetchedLandMark = ref.watch(surveyController).fetchedLandMark;
    String fetchedLat = ref.watch(surveyController).fetchedLat;
    String fetchedLong = ref.watch(surveyController).fetchedLong;
    String fetchedAltitude = ref.watch(surveyController).fetchedAltitude;
    final TextEditingController transNoController =
        ref.watch(surveyController).transNoController;
    final TextEditingController transCapacityController =
        ref.watch(surveyController).transCapacityController;

    return Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          backgroundColor: lightBlue,
          elevation: 0.0,
          centerTitle: true,
          titleTextStyle: TextStyle(
            color: bDarkBlue,
            fontSize: 18.0,
          ),
          title: Text(
            'Transformer Asset Survey',
            style: TextStyle(color: bDarkBlue),
          ),
          actions: [
            // Export to Excel button
            IconButton(
              onPressed: () async {
                await showExportDialog(context);
              },
              icon: const Icon(
                Icons.file_download,
                color: Colors.green,
              ),
            ),
            IconButton(
              onPressed: () async {},
              icon: Icon(
                hasInternet ? Icons.cloud : Icons.cloud_off,
                color: hasInternet ? Colors.green : Colors.red,
              ),
            )
          ],
        ),
        body: WillPopScope(
            onWillPop: () async {
              ref.read(surveyController).clearAllUpdatedSelectedFields();
              // Navigator.of(context).pop(true);
              return true;
            },
            child: SafeArea(
                child: SingleChildScrollView(
                    child: Column(mainAxisSize: MainAxisSize.min, children: [
              Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: 10.0, vertical: 10.0),
                child: GestureDetector(
                  child: BoxContainer.rectangleContainer(
                      '${dataBloc.region} > ${dataBloc.zone} > ${dataBloc.ward}'),
                  onTap: () {},
                ),
              ),
              Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10.0, vertical: 6.0),
                      child: TextField(
                        maxLength: 15,
                        controller: transNoController,
                        obscureText: false,
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 14,
                          overflow: TextOverflow.ellipsis,
                        ),
                        onChanged: (newValue) {
                          log('trans No $newValue');
                          ref.read(surveyController).updateSurveyFieldValues(
                              context, ref, newValue.toUpperCase(), 'transNo');
                        },
                        decoration: InputDecoration(
                          prefixText: 'TFR',
                          counterText: '',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          labelText: 'Transformer Number *',
                          labelStyle: TextStyle(
                              fontSize: 17,
                              color: darkBlue,
                              fontWeight: FontWeight.bold),
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10.0, vertical: 6.0),
                      child: TextField(
                        controller: transCapacityController,
                        obscureText: false,
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 14,
                          overflow: TextOverflow.ellipsis,
                        ),
                        onChanged: (newValue) {
                          log('trans Capacity $newValue');
                          ref.read(surveyController).updateSurveyFieldValues(
                              context, ref, newValue, 'transCapacity');
                        },
                        keyboardType: TextInputType.number,
                        maxLength: 4,
                        decoration: InputDecoration(
                          counterText: '',
                          suffixText: 'kW',
                          suffixStyle: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          labelText: 'Transformer Capacity',
                          labelStyle: TextStyle(
                              fontSize: 17,
                              color: darkBlue,
                              fontWeight: FontWeight.bold),
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10.0, vertical: 8.0),
                child: SizedBox(
                  width: deviceWidth,
                  child: InputDecorator(
                      decoration: InputDecoration(
                        label: RichText(
                            text: TextSpan(
                          text: 'Location',
                          style: TextStyle(
                              fontSize: 17,
                              color: darkBlue,
                              fontWeight: FontWeight.bold),
                        )),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10.0),
                        ),
                      ),
                      child: Column(
                        children: [
                          AnimatedTextField(
                            animationType: Animationtype.typer,
                            keyboardType: TextInputType
                                .visiblePassword, // to remove emoji from the keyboard
                            maxLength: 100,

                            controller: surveyManualLocationController,
                            hintTexts: const [
                              'Provide additional landmark information if any',
                            ],
                            style: const TextStyle(color: Colors.black),
                            hintTextStyle: const TextStyle(
                              fontSize: 13,
                            ),
                            onChanged: (value) {
                              ref
                                  .read(surveyController)
                                  .updateSurveyFieldValues(context, ref, value,
                                      'manualEnteredLocation');
                            },

                            decoration: InputDecoration(
                                counterText: '',
                                prefixIcon:
                                    const Icon(Icons.location_on_outlined),
                                contentPadding: const EdgeInsets.all(16.0),
                                filled: true,
                                fillColor:
                                    const Color.fromARGB(248, 64, 124, 161)
                                        .withOpacity(0.20),
                                focusedBorder: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                      color: Color.fromARGB(248, 64, 124, 161),
                                      width: 1.0),
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                      color: Color.fromARGB(248, 64, 124, 161),
                                      width: 1.0),
                                  borderRadius: BorderRadius.circular(8.0),
                                )),
                          ),
                          const SizedBox(height: 20),
                          isSurveyLocationFetched == true
                              ? Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    fetchedLandMark != ''
                                        ? Text(
                                            fetchedLandMark,
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                                fontSize: 13,
                                                color: bDarkBlue,
                                                fontWeight: FontWeight.bold),
                                          )
                                        : Container(),
                                    const SizedBox(
                                      height: 2,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceEvenly,
                                      children: [
                                        const Text(
                                          'Latitude : ',
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            fontSize: 12,
                                          ),
                                        ),
                                        Text(
                                          fetchedLat,
                                          textAlign: TextAlign.center,
                                          style: const TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.bold),
                                        ),
                                        const Text(
                                          'Longitude : ',
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            fontSize: 12,
                                          ),
                                        ),
                                        Text(
                                          fetchedLong,
                                          textAlign: TextAlign.center,
                                          style: const TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 2,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const Text(
                                          'Altitude : ',
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            fontSize: 12,
                                          ),
                                        ),
                                        Text(
                                          fetchedAltitude,
                                          textAlign: TextAlign.center,
                                          style: const TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ],
                                    ),
                                  ],
                                )
                              : Center(
                                  child: Text(
                                  'Fetching Location...',
                                  style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: bDarkBlue),
                                ))
                        ],
                      )),
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 8.0, horizontal: 10),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.12,
                  padding: const EdgeInsets.all(8.0),
                  margin: const EdgeInsets.only(left: 3, right: 3),
                  decoration: BoxDecoration(
                    border: Border.all(color: bDarkBlue),
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Stack(
                    children: [
                      const Positioned.fill(
                        child: Align(
                            alignment: Alignment.topLeft,
                            child: Text(
                              'Remarks / Comments ',
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 14,
                              ),
                            )),
                      ),
                      TextField(
                        maxLength: 250,
                        controller: commentsController,
                        style:
                            const TextStyle(color: Colors.black, fontSize: 12),
                        onChanged: (value) {
                          ref.read(surveyController).updateSurveyFieldValues(
                              context, ref, value, "comments");
                        },
                        decoration: const InputDecoration(
                          counterText: '',
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(
                height: 29,
              ),
              GestureDetector(
                  child: Container(
                    height: 50,
                    width: deviceWidth * 0.8,
                    decoration: BoxDecoration(
                        color: const Color.fromARGB(248, 64, 124, 161)
                            .withOpacity(0.8),
                        borderRadius: BorderRadius.circular(10)),
                    child: const Center(
                      child: Text(
                        'Proceed to Photo',
                        style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16),
                      ),
                    ),
                  ),
                  onTap: () async {
                    ref.read(surveyController).onClickEventIsFor('transSurvey');
                    // Check if location is available
                    if (fetchedLat == '' || fetchedLong == '') {
                      alertPopUp(context, 'Collecting Data Please Wait',
                          'assets/animation/userAlert.json');
                      return;
                    }
                    if (transCapacityController.text.contains('.')) {
                      FocusScope.of(context).unfocus();
                      showToastMessage(
                        context,
                        "Transformer Capacity must not contain a decimal value.",
                      );
                      return;
                    }
                    if (transNoController.text.isEmpty) {
                      FocusScope.of(context).unfocus();
                      showToastMessage(
                          context, "Please provide the Transformer number");
                      return;
                    } else {
                      ref.read(surveyController).updateSurveyFieldValues(
                            context,
                            ref,
                            'TFR${transNoController.text.trim()}',
                            'transNo',
                          );
                    }

                    // All validations passed
                    await Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) =>
                              const CaptureThreeImagesScreen()),
                    );
                  }),
              const SizedBox(
                height: 10,
              ),
            ])))));
  }
}
