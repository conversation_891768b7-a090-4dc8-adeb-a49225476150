import 'dart:async';
import 'dart:developer';
import 'package:animated_hint_textfield/animated_hint_textfield.dart';
import 'package:barcode_scan2/barcode_scan2.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as r;
import 'package:provider/provider.dart';
import 'package:schnell_pole_installation/Provider/provider.dart';
import 'package:schnell_pole_installation/survey/survey_controller.dart';
import 'package:schnell_pole_installation/survey/background_sync_manager.dart';
import 'package:schnell_pole_installation/survey/excel_export.dart';
import 'package:schnell_pole_installation/utils/box_container.dart';
import 'package:schnell_pole_installation/utils/constants.dart';
import 'package:schnell_pole_installation/utils/dialog_box.dart';
import 'package:schnell_pole_installation/utils/loader.dart';
import 'survey_take_photo.dart';

class PoleSurveyInformation extends r.ConsumerStatefulWidget {
  const PoleSurveyInformation({super.key});
  @override
  r.ConsumerState<PoleSurveyInformation> createState() => _PoleSurveyState();
}

class _PoleSurveyState extends r.ConsumerState<PoleSurveyInformation>
    with WidgetsBindingObserver {
  // late List<ConnectivityResult> connectivityResult;
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;
  List<ConnectivityResult> _connectionStatus = [ConnectivityResult.none];
  final Connectivity _connectivity = Connectivity();
  bool hasInternet = false;
  // Background sync manager
  final BackgroundSyncManager _syncManager = BackgroundSyncManager.instance;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(surveyController).fetchLocationForSurvey();
      // Check initial connectivity status
      _checkInitialConnectivity();
      // Initialize background sync manager
      // _initializeBackgroundSync();
    });
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }

  /// Initialize background sync manager
  Future<void> _initializeBackgroundSync() async {
    try {
      await _syncManager.initialize();
      log('Background sync manager initialized for pole survey');
    } catch (e) {
      log('Failed to initialize background sync manager: $e');
    }
  }

  /// Check initial connectivity status when screen loads
  Future<void> _checkInitialConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      if (mounted) {
        await _updateConnectionStatus(result);
      }
    } catch (e) {
      log('Error checking initial connectivity: $e');
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // App came back to foreground, check connectivity
      log('App resumed - checking connectivity status');
      _checkConnectivityOnResume();
    }
  }

  /// Check connectivity when app resumes or screen comes back into focus
  Future<void> _checkConnectivityOnResume() async {
    try {
      final result = await _connectivity.checkConnectivity();
      if (mounted) {
        await _updateConnectionStatus(result);
      }
    } catch (e) {
      log('Error checking connectivity on resume: $e');
    }
  }

  Future<void> initConnectivity() async {
    late List<ConnectivityResult> result;
    // Platform messages may fail, so we use a try/catch PlatformException.
    try {
      result = await _connectivity.checkConnectivity();
      debugPrint('Connection Status : $result');
    } on PlatformException catch (e) {
      debugPrint(
        'Couldn\'t check connectivity status : $e',
      );
      return;
    }
    if (!mounted) {
      return Future.value(null);
    }
    return _updateConnectionStatus(result);
  }

  // Debounce mechanism for connectivity changes
  static DateTime? _lastConnectivityChange;
  static const Duration _connectivityDebounceDelay = Duration(seconds: 3);

  Future<void> _updateConnectionStatus(List<ConnectivityResult> result) async {
    if (!mounted) return;

    var dataBloc = Provider.of<DataModel>(context, listen: false);
    setState(() {
      _connectionStatus = result;
    });
    debugPrint('Update Install Connection Status : $_connectionStatus');

    if (_connectionStatus.contains(ConnectivityResult.none) ||
        _connectionStatus.isEmpty) {
      if (mounted) {
        setState(() {
          hasInternet = false;
        });
      }
      dataBloc.hasInternet = false;
      log('Internet connection lost');
    } else {
      // Debounce connectivity changes to prevent rapid upload attempts
      final now = DateTime.now();
      if (_lastConnectivityChange != null &&
          now.difference(_lastConnectivityChange!) <
              _connectivityDebounceDelay) {
        log('Connectivity change debounced - too soon since last change');
        if (mounted) {
          setState(() {
            hasInternet = true;
          });
        }
        dataBloc.hasInternet = true;
        return;
      }
      _lastConnectivityChange = now;

      log('Internet connection restored - triggering background sync');

      // Trigger background sync when internet is restored
      // _syncManager.startBackgroundSync();

      if (mounted) {
        setState(() {
          hasInternet = true;
        });
      }
      dataBloc.hasInternet = true;
    }
  }

  bool isNumeric(String newValue) {
    if (newValue.isEmpty) {
      return false;
    }
    return int.tryParse(newValue) != null;
  }

  // Get initial wattage for a given lamp type
  String getInitialLampWattage(String type) {
    return lampWattages[type]?.first ?? '40 W';
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _connectivitySubscription.cancel();
    // Background sync manager will continue running in background
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double deviceWidth = MediaQuery.of(context).size.width;
    var dataBloc = Provider.of<DataModel>(context, listen: false);
    final TextEditingController clampTypeWidthController =
        ref.watch(surveyController).clampTypeWidthController;
    final TextEditingController surveyManualLocationController =
        ref.watch(surveyController).surveyManualLocationEntry;
    final TextEditingController clampTypeLengthController =
        ref.watch(surveyController).clampTypeLengthController;
    final TextEditingController goodArmTextController =
        ref.watch(surveyController).goodArmTextController;
    final TextEditingController badArmTextController =
        ref.watch(surveyController).badArmTextController;
    final TextEditingController missingArmTextController =
        ref.watch(surveyController).missingArmTextController;
    final TextEditingController workingTextController =
        ref.watch(surveyController).workingTextController;
    final TextEditingController commentsController =
        ref.watch(surveyController).commentsController;
    final TextEditingController poleNumberController =
        ref.watch(surveyController).poleNumberController;
    final TextEditingController exCorpPoleNoController =
        ref.watch(surveyController).exCorpPoleNoController;
    final TextEditingController switchPointController =
        ref.watch(surveyController).switchPointController;
    final TextEditingController notWorkingTextController =
        ref.watch(surveyController).notWorkingTextController;
    final TextEditingController missingTextController =
        ref.watch(surveyController).missingTextController;
    final TextEditingController poleMotorRatingController =
        ref.watch(surveyController).poleMotorRatingController;
    final TextEditingController poleMotorMakeController =
        ref.watch(surveyController).poleMotorMakeController;
    final TextEditingController poleMotorModelController =
        ref.watch(surveyController).poleMotorModelController;
    final TextEditingController poleTransformerNoController =
        ref.watch(surveyController).poleTransformerNoController;
    String poleNoTemplate = dataBloc.poleNoTemplate;
    String wardNumber = dataBloc.wardNumberForPolePrefix;

    String polePrefix = ref
        .read(surveyController)
        .extractPolePrefix(poleNoTemplate, wardNumber);
    log(polePrefix);
    String transformerNoPrefixText = 'TFR';
    String spNoPrefixText = 'SP';
    String selectedClampUnits = ref.watch(surveyController).selectedClampUnits;
    String selectedClampWidth = ref.watch(surveyController).selectedClampWidth;
    String selectedClampLength =
        ref.watch(surveyController).selectedClampLength;
    String selectedClampType = ref.watch(surveyController).selectedClampType;
    String selectedBracketMountingHeight =
        ref.watch(surveyController).selectedBracketMountingHeight;
    String selectedCustomBracketHeight =
        ref.watch(surveyController).selectedCustomBracketHeight;
    bool isEarthingRequired = ref.watch(surveyController).isEarthingRequired;
    String selectedManualSwitchCtrl =
        ref.watch(surveyController).selectedManualSwitchCtrl;
    String selectedPoleCondition =
        ref.watch(surveyController).selectedPoleCondition;
    String selectedArmCount = ref.watch(surveyController).selectedArmCount;
    String selectedArmLength = ref.watch(surveyController).selectedArmLength;
    String selectedPoleHeight = ref.watch(surveyController).selectedPoleHeight;
    String selectedPoleSpan = ref.watch(surveyController).selectedPoleSpan;
    String selectedPoleType = ref.watch(surveyController).selectedPoleType;
    String selectedSwitchPointNo =
        ref.watch(surveyController).selectedSwitchPointNo;

    List<Map<String, String>> selectedLamps =
        ref.watch(surveyController).selectedLamps;
    // String selectedLampWatts = ref.watch(surveyController).selectedLampWatts;
    String selectedPoleMotorCondition =
        ref.watch(surveyController).selectedPoleMotorCondition;
    String selectedIncomingTransLine =
        ref.watch(surveyController).selectedIncomingTransLine;
    String selectedIncomingTransType =
        ref.watch(surveyController).selectedIncomingTransType;
    String selectedPoleWinchCondition =
        ref.watch(surveyController).selectedPoleWinchCondition;
    String selectedPoleRopeCondition =
        ref.watch(surveyController).selectedPoleRopeCondition;
    String selectedPoleRatingUnits =
        ref.watch(surveyController).selectedPoleRatingUnits;

    bool isSurveyLocationFetched =
        ref.watch(surveyController).isSurveyLocationFetched;
    String fetchedLandMark = ref.watch(surveyController).fetchedLandMark;
    String fetchedLat = ref.watch(surveyController).fetchedLat;
    String fetchedLong = ref.watch(surveyController).fetchedLong;
    String fetchedAltitude = ref.watch(surveyController).fetchedAltitude;

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: lightBlue,
        elevation: 0.0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: bDarkBlue,
          fontSize: 18.0,
        ),
        title: Text(
          'Pole Asset Survey',
          style: TextStyle(color: bDarkBlue),
        ),
        actions: [
          // Export to Excel button
          IconButton(
            onPressed: () async {
              await showExportDialog(context);
            },
            icon: const Icon(
              Icons.file_download,
              color: Colors.green,
            ),
            tooltip: 'Export to Excel',
          ),
          IconButton(
            onPressed: () async {},
            icon: Icon(
              hasInternet ? Icons.cloud : Icons.cloud_off,
              color: hasInternet ? Colors.green : Colors.red,
            ),
          )
        ],
      ),
      body: WillPopScope(
        onWillPop: () async {
          ref.read(surveyController).clearAllUpdatedSelectedFields();
          // Navigator.of(context).pop(true);
          return true;
        },
        child: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 10.0, vertical: 10.0),
                  child: GestureDetector(
                    child: BoxContainer.rectangleContainer(
                        '${dataBloc.region} > ${dataBloc.zone} > ${dataBloc.ward}'),
                    onTap: () {},
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 10.0, vertical: 6.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: TextField(
                            maxLength: 4,
                            controller: poleNumberController,
                            obscureText: false,
                            style: const TextStyle(
                              color: Colors.black,
                              fontSize: 14,
                              overflow: TextOverflow.ellipsis,
                            ),
                            onChanged: (newValue) {
                              log('pole name $newValue');
                              ref
                                  .read(surveyController)
                                  .updateSurveyFieldValues(
                                      context, ref, newValue, 'poleNumber');
                            },
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              counterText: '',
                              prefixText: polePrefix,
                              prefixStyle: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                              suffixIcon: InkWell(
                                  child: Icon(Icons.qr_code,
                                      size: 23, color: darkBlue),
                                  onTap: () async {
                                    ScanResult result =
                                        await BarcodeScanner.scan();
                                    if (result.rawContent != '') {
                                      // bool isValidPole = await ref
                                      //     .read(surveyController)
                                      //     .isValidPoleNumber(result.rawContent);
                                      // if (isValidPole) {
                                      final regex = RegExp(r'^\d{4}$');
                                      if (!regex
                                          .hasMatch(result.rawContent.trim())) {
                                        if (context.mounted) {
                                          FocusScope.of(context).unfocus();
                                        }
                                        if (context.mounted) {
                                          showToastMessage(
                                            context,
                                            "Invalid pole number. It must contain only 4 digits.",
                                          );
                                        }
                                        return;
                                      }
                                      log('pole name $result.rawContent');
                                      ref
                                          .read(surveyController)
                                          .updateSurveyFieldValues(
                                            context,
                                            ref,
                                            result.rawContent,
                                            "poleNumber",
                                          );
                                      // } else {
                                      //   alertPopUp1(
                                      //       context,
                                      //       'Invalid Pole Number format. Please enter a valid Pole Number format (BBMPSNLW99P9999)',
                                      //       'assets/animation/warn.json');
                                      // }
                                    }
                                  }),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              labelText: 'Pole Number *',
                              labelStyle: TextStyle(
                                  fontSize: 17,
                                  color: darkBlue,
                                  fontWeight: FontWeight.bold),
                              floatingLabelBehavior:
                                  FloatingLabelBehavior.always,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 10.0, vertical: 6.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          // readOnly: false,
                          obscureText: false,
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 14,
                            overflow: TextOverflow.ellipsis,
                          ),
                          onChanged: (newValue) {
                            log('escomPoleNumber $newValue');
                            ref.read(surveyController).updateSurveyFieldValues(
                                context, ref, newValue, 'escomPoleNumber');
                          },
                          maxLength: 25,

                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            counterText: '',
                            labelText: 'EB Pole No',
                            labelStyle: TextStyle(
                                fontSize: 17,
                                color: darkBlue,
                                fontWeight: FontWeight.bold),
                            floatingLabelBehavior: FloatingLabelBehavior.always,
                          ),
                        ),
                      ),
                      const SizedBox(
                        width: 15,
                      ),
                      Expanded(
                        child: TextField(
                          // readOnly: false,
                          controller: exCorpPoleNoController,
                          keyboardType: TextInputType.number,
                          obscureText: false,
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 14,
                            overflow: TextOverflow.ellipsis,
                          ),
                          onChanged: (newValue) {
                            log('exCorpPoleNo $newValue');
                            ref.read(surveyController).updateSurveyFieldValues(
                                context, ref, newValue, 'exCorpPoleNo');
                          },
                          maxLength: 4,

                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            counterText: '',
                            labelText: 'Ex Corp Pole No',
                            labelStyle: TextStyle(
                                fontSize: 17,
                                color: darkBlue,
                                fontWeight: FontWeight.bold),
                            floatingLabelBehavior: FloatingLabelBehavior.always,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                      vertical: 8.0, horizontal: 10.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          // readOnly: false,
                          controller: poleTransformerNoController,
                          obscureText: false,
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 14,
                            overflow: TextOverflow.ellipsis,
                          ),
                          onChanged: (newValue) {
                            log('poleTransFormerNo $newValue');
                            ref.read(surveyController).updateSurveyFieldValues(
                                context,
                                ref,
                                newValue.toUpperCase(),
                                'poleTransFormerNo');
                          },
                          maxLength: 15,

                          decoration: InputDecoration(
                            prefixText: transformerNoPrefixText,
                            prefixStyle: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            counterText: '',
                            labelText: 'Transformer No',
                            labelStyle: TextStyle(
                                fontSize: 17,
                                color: darkBlue,
                                fontWeight: FontWeight.bold),
                            floatingLabelBehavior: FloatingLabelBehavior.always,
                          ),
                        ),
                      ),
                      const SizedBox(
                        width: 15,
                      ),
                      Expanded(
                        child: TextField(
                          keyboardType: TextInputType.number,
                          // readOnly: false,
                          controller: switchPointController,
                          obscureText: false,
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 14,
                            overflow: TextOverflow.ellipsis,
                          ),
                          onChanged: (newValue) {
                            log('switchPointNo $newValue');
                            ref.read(surveyController).updateSurveyFieldValues(
                                context,
                                ref,
                                newValue.toUpperCase(),
                                'switchPointNo');
                          },
                          maxLength: 3,
                          decoration: InputDecoration(
                            prefixText: spNoPrefixText,
                            prefixStyle: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            counterText: '',
                            // hintText: 'eg : SP999',
                            labelText: 'Switch Point No',
                            labelStyle: TextStyle(
                                fontSize: 17,
                                color: darkBlue,
                                fontWeight: FontWeight.bold),
                            floatingLabelBehavior: FloatingLabelBehavior.always,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 10.0, vertical: 8.0),
                  child: SizedBox(
                    width: deviceWidth,
                    child: InputDecorator(
                        decoration: InputDecoration(
                          label: RichText(
                              text: TextSpan(
                            text: 'Location',
                            style: TextStyle(
                                fontSize: 17,
                                color: darkBlue,
                                fontWeight: FontWeight.bold),
                          )),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10.0),
                          ),
                        ),
                        child: Column(
                          children: [
                            AnimatedTextField(
                              animationType: Animationtype.typer,
                              keyboardType: TextInputType
                                  .visiblePassword, // to remove emoji from the keyboard
                              maxLength: 100,

                              controller: surveyManualLocationController,
                              hintTexts: const [
                                'Provide additional landmark information if any',
                              ],
                              style: const TextStyle(color: Colors.black),
                              hintTextStyle: const TextStyle(
                                fontSize: 13,
                              ),
                              onChanged: (value) {
                                ref
                                    .read(surveyController)
                                    .updateSurveyFieldValues(context, ref,
                                        value, 'manualEnteredLocation');
                              },

                              decoration: InputDecoration(
                                  counterText: '',
                                  prefixIcon:
                                      const Icon(Icons.location_on_outlined),
                                  contentPadding: const EdgeInsets.all(16.0),
                                  filled: true,
                                  fillColor:
                                      const Color.fromARGB(248, 64, 124, 161)
                                          .withOpacity(0.20),
                                  focusedBorder: OutlineInputBorder(
                                    borderSide: const BorderSide(
                                        color:
                                            Color.fromARGB(248, 64, 124, 161),
                                        width: 1.0),
                                    borderRadius: BorderRadius.circular(8.0),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: const BorderSide(
                                        color:
                                            Color.fromARGB(248, 64, 124, 161),
                                        width: 1.0),
                                    borderRadius: BorderRadius.circular(8.0),
                                  )),
                            ),
                            const SizedBox(height: 20),
                            isSurveyLocationFetched == true
                                ? Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      fetchedLandMark != ''
                                          ? Text(
                                              fetchedLandMark,
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                  fontSize: 13,
                                                  color: bDarkBlue,
                                                  fontWeight: FontWeight.bold),
                                            )
                                          : Container(),
                                      const SizedBox(
                                        height: 2,
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceEvenly,
                                        children: [
                                          const Text(
                                            'Latitude : ',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              fontSize: 12,
                                            ),
                                          ),
                                          Text(
                                            fetchedLat,
                                            textAlign: TextAlign.center,
                                            style: const TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold),
                                          ),
                                          const Text(
                                            'Longitude : ',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              fontSize: 12,
                                            ),
                                          ),
                                          Text(
                                            fetchedLong,
                                            textAlign: TextAlign.center,
                                            style: const TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(
                                        height: 2,
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          const Text(
                                            'Altitude : ',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              fontSize: 12,
                                            ),
                                          ),
                                          Text(
                                            fetchedAltitude,
                                            textAlign: TextAlign.center,
                                            style: const TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold),
                                          ),
                                        ],
                                      ),
                                    ],
                                  )
                                : Center(
                                    child: Text(
                                    'Fetching Location...',
                                    style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: bDarkBlue),
                                  ))
                          ],
                        )),
                  ),
                ),
                Row(
                  children: [
                    Expanded(
                      child: BoxContainer.buildDropdown(
                        context,
                        value: selectedPoleCondition,
                        label: "Pole Condition",
                        items: poleConditions,
                        onChanged: (newValue) {
                          log('selected value $newValue');

                          ref.read(surveyController).updateSurveyFieldValues(
                              context, ref, newValue, 'poleCondition');
                        },
                      ),
                    ),
                    if (selectedPoleCondition != 'Missing')
                      Expanded(
                          child: BoxContainer.buildDropdown(
                        context,
                        value: selectedPoleType,
                        label: "Pole Type",
                        items: poleTypes,
                        onChanged: (newValue) {
                          log('selected value $newValue');

                          ref.read(surveyController).updateSurveyFieldValues(
                              context, ref, newValue, 'poleType');
                          // ref
                          //     .read(surveyController)
                          //     .updatePoleTypeCascade(context, ref, newValue!);
                        },
                      )),
                  ],
                ),

                if (selectedPoleCondition != 'Missing')
                  Row(
                    children: [
                      if (selectedPoleType != 'High Mast(HM)' &&
                          selectedPoleType != 'Mini Mast(MM)')
                        Expanded(
                          child: BoxContainer.buildDropdown(
                            context,
                            // value: selectedPoleSpan,
                            label: "Pole Span *",
                            items: poleSpan,
                            onChanged: (newValue) {
                              log('selected value $newValue');

                              ref
                                  .read(surveyController)
                                  .updateSurveyFieldValues(
                                      context, ref, newValue, 'poleSpan');
                            },
                          ),
                        ),
                      Expanded(
                          child: BoxContainer.buildDropdown(
                        context,
                        value: selectedPoleHeight,
                        label: "Pole Height",
                        items: poleHeightOptions,
                        onChanged: (newValue) {
                          log('selected value $newValue');

                          ref.read(surveyController).updateSurveyFieldValues(
                              context, ref, newValue, 'poleHeight');
                        },
                      )),
                    ],
                  ),
                if (selectedPoleType == 'High Mast(HM)')
                  Row(
                    children: [
                      Expanded(
                        child: BoxContainer.buildDropdown(
                          context,
                          value: selectedPoleMotorCondition,
                          label: "Motor Condition",
                          items: motorConditionOptions,
                          onChanged: (newValue) {
                            log('selected value $newValue');
                            ref.read(surveyController).updateSurveyFieldValues(
                                context, ref, newValue, 'motorCondition');
                          },
                        ),
                      ),
                    ],
                  ),
                if (selectedPoleType == 'High Mast(HM)' &&
                    selectedPoleMotorCondition != 'Missing')
                  Card(
                    color: Theme.of(context).primaryColor,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8)),
                    margin: const EdgeInsets.all(10),
                    elevation: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            "Motor Details",
                            style: TextStyle(
                                fontSize: 15, fontWeight: FontWeight.w500),
                          ),
                          const SizedBox(height: 10),
                          Row(
                            children: [
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10.0, vertical: 6.0),
                                  child: TextField(
                                    keyboardType: TextInputType.number,
                                    controller: poleMotorRatingController,
                                    obscureText: false,
                                    maxLength: 4,
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontSize: 14,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    onChanged: (newValue) {
                                      log('Motor Rating $newValue');
                                      ref
                                          .read(surveyController)
                                          .updateSurveyFieldValues(context, ref,
                                              newValue, 'motorRating');
                                    },
                                    decoration: InputDecoration(
                                      counterText: '',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      labelText: 'Rating',
                                      labelStyle: TextStyle(
                                          fontSize: 17,
                                          color: darkBlue,
                                          fontWeight: FontWeight.bold),
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                  child: BoxContainer.buildDropdown(
                                context,
                                value: selectedPoleRatingUnits,
                                label: "Rating Units",
                                items: poleMotorRatingUnitsOptions,
                                onChanged: (newValue) {
                                  log('motor rating units $newValue');

                                  ref
                                      .read(surveyController)
                                      .updateSurveyFieldValues(context, ref,
                                          newValue, 'ratingUnits');
                                },
                              )),
                            ],
                          ),
                          Row(
                            children: [
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10.0, vertical: 6.0),
                                  child: TextField(
                                    controller: poleMotorMakeController,
                                    obscureText: false,
                                    maxLength: 15,
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontSize: 14,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    onChanged: (newValue) {
                                      log('Motor Make $newValue');
                                      ref
                                          .read(surveyController)
                                          .updateSurveyFieldValues(context, ref,
                                              newValue, 'motorMake');
                                    },
                                    decoration: InputDecoration(
                                      counterText: '',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      labelText: 'Make',
                                      labelStyle: TextStyle(
                                          fontSize: 17,
                                          color: darkBlue,
                                          fontWeight: FontWeight.bold),
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10.0, vertical: 6.0),
                                  child: TextField(
                                    maxLength: 15,
                                    controller: poleMotorModelController,
                                    obscureText: false,
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontSize: 14,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    onChanged: (newValue) {
                                      log('Motor Model $newValue');

                                      ref
                                          .read(surveyController)
                                          .updateSurveyFieldValues(context, ref,
                                              newValue, 'motorModel');
                                    },
                                    decoration: InputDecoration(
                                      counterText: '',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      labelText: 'Model',
                                      labelStyle: TextStyle(
                                          fontSize: 17,
                                          color: darkBlue,
                                          fontWeight: FontWeight.bold),
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                if (selectedPoleType == 'High Mast(HM)')
                  Row(
                    children: [
                      Expanded(
                          child: BoxContainer.buildDropdown(
                        context,
                        value: selectedPoleWinchCondition,
                        label: "Winch Condition",
                        items: poleWinchConditionOptions,
                        onChanged: (newValue) {
                          log('winch condition $newValue');

                          ref.read(surveyController).updateSurveyFieldValues(
                              context, ref, newValue, 'winchCondition');
                        },
                      )),
                      Expanded(
                          child: BoxContainer.buildDropdown(
                        context,
                        value: selectedPoleRopeCondition,
                        label: "Rope Condition",
                        items: poleRopeConditionOptions,
                        onChanged: (newValue) {
                          log('rope condition $newValue');

                          ref.read(surveyController).updateSurveyFieldValues(
                              context, ref, newValue, 'ropeCondition');
                        },
                      )),
                    ],
                  ),
                if (selectedPoleCondition != 'Missing')
                  Row(
                    children: [
                      Expanded(
                        child: BoxContainer.buildDropdown(
                          context,
                          value: selectedIncomingTransLine,
                          label: "Incoming\nTransmission Line",
                          items: incomingTransLineOptions,
                          onChanged: (newValue) {
                            log('selected value $newValue');

                            ref.read(surveyController).updateSurveyFieldValues(
                                context, ref, newValue, 'incomingTransLine');
                          },
                        ),
                      ),
                      Expanded(
                        child: BoxContainer.buildDropdown(
                          context,
                          value: selectedIncomingTransType,
                          label: "Incoming\nTransmission Type",
                          items: incomingTransTypeOptions,
                          onChanged: (newValue) {
                            log('selected value $newValue');

                            ref.read(surveyController).updateSurveyFieldValues(
                                context, ref, newValue, 'incomingTransType');
                          },
                        ),
                      ),
                    ],
                  ),
                if (selectedPoleCondition != 'Missing' &&
                    (selectedPoleType != 'High Mast(HM)' &&
                        selectedPoleType != 'Mini Mast(MM)'))
                  BoxContainer.buildDropdown(
                    context,
                    value: selectedClampType,
                    label: "Clamp Dimension",
                    items: clampTypeOptions,
                    onChanged: (newValue) {
                      log('selected value $newValue');

                      ref.read(surveyController).updateSurveyFieldValues(
                          context, ref, newValue, 'clampType');
                    },
                  ),
                if (selectedPoleCondition != 'Missing')
                  if (selectedClampType == 'Others')
                    const SizedBox(
                      height: 10,
                    ),
                if (selectedPoleCondition != 'Missing')
                  if (selectedClampType == 'Others')
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          height: 47,
                          width: 54,
                          child: TextFormField(
                            controller: clampTypeLengthController,
                            style: const TextStyle(color: Colors.black),
                            textAlign: TextAlign.center,
                            keyboardType: TextInputType.number,
                            maxLength: 2,
                            cursorColor: Theme.of(context).primaryColor,
                            decoration: InputDecoration(
                                errorStyle: const TextStyle(fontSize: 4),
                                border: const OutlineInputBorder(),
                                floatingLabelBehavior:
                                    FloatingLabelBehavior.always,
                                counterText: '',
                                labelText: '<L>',
                                labelStyle: TextStyle(
                                    color: darkBlue,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold)),
                            onChanged: (newValue) {
                              if (newValue.length == 1 ||
                                  newValue.length == 2) {
                                // FocusScope.of(context).nextFocus();
                                ref
                                    .read(surveyController)
                                    .updateSurveyFieldValues(
                                        context,
                                        ref,
                                        clampTypeLengthController.text,
                                        'customInputLength');
                              }
                            },
                          ),
                        ),
                        const SizedBox(
                          width: 14,
                        ),
                        const Text('x', style: TextStyle(fontSize: 21)),
                        const SizedBox(
                          width: 14,
                        ),
                        SizedBox(
                          height: 47,
                          width: 54,
                          child: TextFormField(
                            controller: clampTypeWidthController,
                            style: const TextStyle(color: Colors.black),
                            // autofocus: autoFocus,
                            textAlign: TextAlign.center,
                            keyboardType: TextInputType.number,
                            // controller: controller,
                            maxLength: 2,
                            cursorColor: Theme.of(context).primaryColor,
                            decoration: InputDecoration(
                                errorStyle: const TextStyle(fontSize: 4),
                                border: const OutlineInputBorder(),
                                floatingLabelBehavior:
                                    FloatingLabelBehavior.always,
                                counterText: '',
                                labelText: '<W>',
                                labelStyle: TextStyle(
                                    color: darkBlue,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold)),
                            onChanged: (newValue) {
                              if (newValue.length == 1 ||
                                  newValue.length == 2) {
                                // FocusScope.of(context).nextFocus();
                                ref
                                    .read(surveyController)
                                    .updateSurveyFieldValues(
                                        context,
                                        ref,
                                        clampTypeWidthController.text,
                                        'customInputWidth');
                              }
                            },
                          ),
                        ),
                        const SizedBox(
                          width: 21,
                        ),
                        SizedBox(
                          height: 56,
                          width: 130,
                          child: InputDecorator(
                            decoration: InputDecoration(
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10.0),
                                ),
                                label: RichText(
                                    text: TextSpan(
                                  text: 'Units',
                                  style: TextStyle(
                                      color: darkBlue,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold),
                                ))),
                            child: DropdownButtonHideUnderline(
                                child: DropdownButton2(
                              value: selectedClampUnits,
                              items: units.map((String items) {
                                return DropdownMenuItem(
                                  alignment: AlignmentDirectional.centerStart,
                                  value: items,
                                  child: Text(
                                    items,
                                    style: const TextStyle(color: Colors.black),
                                  ),
                                );
                              }).toList(),
                              onChanged: (newValue) {
                                ref
                                    .read(surveyController)
                                    .updateSurveyFieldValues(context, ref,
                                        newValue, 'customInputUnits');
                              },
                              dropdownStyleData: DropdownStyleData(
                                  padding: const EdgeInsets.only(left: 15),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(14),
                                    color: Colors.white,
                                  )),
                              buttonStyleData: const ButtonStyleData(
                                padding: EdgeInsets.only(),
                                height: 60,
                              ),
                            )),
                          ),
                        ),
                      ],
                    ),

                if (selectedClampType == 'Others')
                  const SizedBox(
                    height: 10,
                  ),

                if (selectedPoleCondition != 'Missing')
                  Row(
                    children: [
                      if (selectedPoleType != 'High Mast(HM)' &&
                          selectedPoleType != 'Mini Mast(MM)')
                        Expanded(
                          child: BoxContainer.buildDropdown(
                            context,
                            value: selectedBracketMountingHeight,
                            label: "Bracket\nMounting Height",
                            items: bracketMountingHeightOptions,
                            onChanged: (newValue) {
                              log('selected value $newValue');

                              ref
                                  .read(surveyController)
                                  .updateSurveyFieldValues(context, ref,
                                      newValue, 'bracketMountingHeight');
                            },
                          ),
                        ),
                      if (selectedPoleCondition != 'Missing')
                        if (selectedPoleType != 'High Mast(HM)' &&
                            selectedPoleType != 'Mini Mast(MM)')
                          if (selectedBracketMountingHeight == 'Others')
                            Padding(
                              padding: const EdgeInsets.only(right: 8.0),
                              child: SizedBox(
                                height: 47,
                                width: 54,
                                child: TextFormField(
                                  style: const TextStyle(color: Colors.black),
                                  textAlign: TextAlign.center,
                                  keyboardType: TextInputType.number,
                                  maxLength: 2,
                                  cursorColor: Theme.of(context).primaryColor,
                                  decoration: InputDecoration(
                                      errorStyle: const TextStyle(fontSize: 4),
                                      border: const OutlineInputBorder(),
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                      counterText: '',
                                      labelText: '<M>',
                                      labelStyle: TextStyle(
                                          color: darkBlue,
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold)),
                                  onChanged: (newValue) {
                                    if (newValue.length == 1 ||
                                        newValue.length == 2) {
                                      ref
                                          .read(surveyController)
                                          .updateSurveyFieldValues(context, ref,
                                              newValue, 'customBracketHeight');
                                    }
                                  },
                                ),
                              ),
                            ),
                    ],
                  ),
                if (selectedPoleCondition != 'Missing')
                  Row(
                    children: [
                      BoxContainer.yesNoToggle(
                          ref, context, 'Earthing', isEarthingRequired),
                      // yesNoToggle(
                      //     ref, context, 'Bracket Required', isBracketRequired),
                    ],
                  ),

                if (selectedPoleCondition != 'Missing')
                  Card(
                    color: Theme.of(context).primaryColor,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8)),
                    margin: const EdgeInsets.all(10),
                    elevation: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            "Arm Details",
                            style: TextStyle(
                                fontSize: 15, fontWeight: FontWeight.w500),
                          ),
                          const SizedBox(height: 10),
                          BoxContainer.buildDropdown(
                            context,
                            value: selectedArmCount,
                            label: "Count",
                            items: armCountOptions,
                            onChanged: (value) {
                              ref
                                  .read(surveyController)
                                  .updateSurveyFieldValues(
                                      context, ref, value, 'armCount');
                            },
                          ),
                          if (selectedArmCount != '0')
                            BoxContainer.buildDropdown(
                              context,
                              value: selectedArmLength,
                              label: "Length",
                              items: ['Normal', 'Small', 'Missing'],
                              onChanged: (value) {
                                ref
                                    .read(surveyController)
                                    .updateSurveyFieldValues(
                                        context, ref, value, 'armLength');
                              },
                            ),
                          if (selectedArmCount != '0')
                            Card(
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8)),
                              color: Colors.grey[100],
                              elevation: 1,
                              child: Padding(
                                padding: const EdgeInsets.all(12),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      "Condition",
                                      style: TextStyle(
                                          fontSize: 15,
                                          fontWeight: FontWeight.w500),
                                    ),
                                    const SizedBox(height: 10),
                                    buildNumberInputRow(ref, context, "Good",
                                        goodArmTextController),
                                    buildNumberInputRow(ref, context, "Bad",
                                        badArmTextController),
                                    buildNumberInputRow(ref, context, "Missing",
                                        missingArmTextController,
                                        enabled: false),
                                  ],
                                ),
                              ),
                            ),
                          // if (selectedArmCount != '0')
                          //   buildDropdown(
                          //     context,
                          //     value: selectedArmCondition,
                          //     label: "Condition",
                          //     items: ['Good', 'Bad', 'Missing'],
                          //     onChanged: (value) {
                          //       ref
                          //           .read(surveyController)
                          //           .updatePoleSurveyFieldValues(
                          //               context, ref, value, 'armCondition');
                          //     },
                          //   ),
                        ],
                      ),
                    ),
                  ),
                if (selectedPoleCondition != 'Missing')
                  if (selectedArmCount != '0')
                    Card(
                      color: Theme.of(context).primaryColor,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8)),
                      margin: const EdgeInsets.all(10),
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              "Light Details",
                              style: TextStyle(
                                  fontSize: 15, fontWeight: FontWeight.w500),
                            ),
                            const SizedBox(height: 10),

                            /// Nested Card: Status and Count
                            Card(
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8)),
                              color: Colors.grey[100],
                              elevation: 1,
                              child: Padding(
                                padding: const EdgeInsets.all(12),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      "Status and Count",
                                      style: TextStyle(
                                          fontSize: 15,
                                          fontWeight: FontWeight.w500),
                                    ),
                                    const SizedBox(height: 10),
                                    buildNumberInputRow(ref, context, "Working",
                                        workingTextController),
                                    buildNumberInputRow(
                                        ref,
                                        context,
                                        "Not Working",
                                        notWorkingTextController),
                                    buildNumberInputRow(ref, context, "Missing",
                                        missingTextController,
                                        enabled: false),
                                  ],
                                ),
                              ),
                            ),
                            if ((workingTextController.text != '0' &&
                                    workingTextController.text != '') ||
                                (notWorkingTextController.text != '0' &&
                                    notWorkingTextController.text != ''))
                              // Multiple Lamp Selection
                              ListView.builder(
                                reverse: true,
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: ref
                                    .watch(surveyController)
                                    .selectedLamps
                                    .length,
                                itemBuilder: (context, index) {
                                  final lamp = ref
                                      .watch(surveyController)
                                      .selectedLamps[index];
                                  return Row(
                                    children: [
                                      // Lamp Type Dropdown
                                      Expanded(
                                        child: lampTypesDropDown(
                                          context,
                                          selectedValue:
                                              lamp['type'] ?? 'Tube light',
                                          items:
                                              lampWattageOptions.keys.toList(),
                                          onChanged: (newType) {
                                            if (newType != null) {
                                              ref
                                                  .read(surveyController)
                                                  .updateLampType(
                                                      index, newType);
                                            }
                                          },
                                          isExpanded: true,
                                        ),
                                      ),
                                      const SizedBox(width: 0),
                                      // Lamp Wattage Dropdown
                                      Expanded(
                                        child: lampTypesDropDown(
                                          context,
                                          selectedValue:
                                              lamp['watts'] ?? '40 W',
                                          items: lampWattageOptions[
                                                  lamp['type']] ??
                                              [],
                                          onChanged: (newWatts) {
                                            if (newWatts != null) {
                                              ref
                                                  .read(surveyController)
                                                  .updateLampWatts(
                                                      index, newWatts);
                                            }
                                          },
                                          isExpanded: true,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 48,
                                        child: (index ==
                                                    selectedLamps.length - 1 &&
                                                (int.parse(selectedArmCount) !=
                                                    0))
                                            ? IconButton(
                                                onPressed: ref
                                                            .watch(
                                                                surveyController)
                                                            .selectedLamps
                                                            .length <
                                                        int.parse(
                                                            selectedArmCount)
                                                    ? () {
                                                        ref
                                                            .read(
                                                                surveyController)
                                                            .addLamp();
                                                      }
                                                    : null,
                                                icon: Icon(
                                                  Icons.add_circle,
                                                  color: ref
                                                              .watch(
                                                                  surveyController)
                                                              .selectedLamps
                                                              .length <
                                                          int.parse(
                                                              selectedArmCount)
                                                      ? const Color.fromARGB(
                                                              248, 64, 124, 161)
                                                          .withOpacity(0.8)
                                                      : Colors.grey,
                                                  size: 30,
                                                ),
                                              )
                                            : null, // Empty space for non-last rows
                                      ),
                                    ],
                                  );
                                },
                              ),
                            const SizedBox(height: 7),
                            Row(
                              children: [
                                Expanded(
                                    child: BoxContainer.buildDropdown(
                                  context,
                                  value: selectedManualSwitchCtrl,
                                  label: "Manual Switch Control",
                                  items: manualSwitchControlOptions,
                                  onChanged: (value) {
                                    ref
                                        .read(surveyController)
                                        .updateSurveyFieldValues(context, ref,
                                            value, 'manualSwitchCtrl');
                                  },
                                )),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8.0, horizontal: 10),
                  child: Container(
                    height: MediaQuery.of(context).size.height * 0.12,
                    padding: const EdgeInsets.all(8.0),
                    margin: const EdgeInsets.only(left: 3, right: 3),
                    decoration: BoxDecoration(
                      border: Border.all(color: bDarkBlue),
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    child: Stack(
                      children: [
                        const Positioned.fill(
                          child: Align(
                              alignment: Alignment.topLeft,
                              child: Text(
                                'Remarks / Comments ',
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 14,
                                ),
                              )),
                        ),
                        TextField(
                          maxLength: 250,
                          controller: commentsController,
                          style: const TextStyle(
                              color: Colors.black, fontSize: 12),
                          onChanged: (value) {
                            ref.read(surveyController).updateSurveyFieldValues(
                                context, ref, value, "comments");
                          },
                          decoration: const InputDecoration(
                            counterText: '',
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(
                  height: 20,
                ),
                GestureDetector(
                    child: Container(
                      height: 50,
                      width: deviceWidth * 0.8,
                      decoration: BoxDecoration(
                          color: const Color.fromARGB(248, 64, 124, 161)
                              .withOpacity(0.8),
                          borderRadius: BorderRadius.circular(10)),
                      child: const Center(
                        child: Text(
                          'Proceed to Photo',
                          style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16),
                        ),
                      ),
                    ),
                    onTap: () async {
                      ref
                          .read(surveyController)
                          .onClickEventIsFor('poleSurvey');
                      // Check if location is available
                      if (fetchedLat == '' || fetchedLong == '') {
                        alertPopUp(context, 'Collecting Data Please Wait',
                            'assets/animation/userAlert.json');
                        return;
                      }

                      // if(){
                      // }

                      // Clamp Type validation
                      if (selectedClampType == 'Others') {
                        if (selectedClampWidth.isEmpty ||
                            selectedClampLength.isEmpty) {
                          FocusScope.of(context).unfocus();
                          showToastMessage(
                              context, "Please provide the length and width");
                          return;
                        }
                        if (!isNumeric(selectedClampLength) ||
                            !isNumeric(selectedClampWidth)) {
                          FocusScope.of(context).unfocus();
                          showToastMessage(
                              context, "Length and width must be numeric");
                          return;
                        }
                      }

                      if ((selectedPoleCondition != 'Missing') &&
                          selectedPoleType != 'High Mast(HM)' &&
                          selectedPoleType != 'Mini Mast(MM)') {
                        if (selectedPoleSpan.isEmpty) {
                          showToastMessage(context, "Please provide Pole Span");
                          return;
                        }

                        // Bracket Mounting Height validation
                        if (selectedBracketMountingHeight == 'Others') {
                          if (selectedCustomBracketHeight.isEmpty) {
                            FocusScope.of(context).unfocus();
                            showToastMessage(context,
                                "Please provide the Meter for bracket mounting height");
                            return;
                          }
                          if (!isNumeric(selectedCustomBracketHeight)) {
                            FocusScope.of(context).unfocus();
                            showToastMessage(context, "Meter must be numeric");
                            return;
                          }
                        }
                      }

                      // Pole number check
                      final poleNumber = poleNumberController.text.trim();
                      if (poleNumber.isEmpty) {
                        showToastMessage(context, "Please provide Pole Number");
                        return;
                      }

                      if (exCorpPoleNoController.text.isNotEmpty) {
                        final regex = RegExp(r'^\d{4}$');
                        if (!regex.hasMatch(exCorpPoleNoController.text)) {
                          FocusScope.of(context).unfocus();
                          showToastMessage(
                            context,
                            "Invalid Ex Corp Pole No. It must contain only 4 digits.",
                          );
                          return;
                        }
                      }

                      // if (switchPointNumber.isEmpty) {
                      //   showToastMessage(
                      //       context, "Please provide Switch Point Number");
                      //   return;
                      // }

                      // switch Point no check
                      if (switchPointController.text.isNotEmpty) {
                        final switchPointNumber =
                            switchPointController.text.trim();
                        final isValidSwitchPoint = await ref
                            .read(surveyController)
                            .isValidSwitchPointNo(switchPointNumber);
                        if (!isValidSwitchPoint) {
                          alertPopUp1(
                            context,
                            'Invalid Switch Point Number format. Please enter a valid format (SP999)',
                            'assets/animation/warn.json',
                          );
                          return;
                        } else {
                          ref.read(surveyController).updateSurveyFieldValues(
                                context,
                                ref,
                                '$spNoPrefixText$switchPointNumber',
                                'switchPointNo',
                              );
                        }
                      }

                      if (poleTransformerNoController.text.isNotEmpty) {
                        ref.read(surveyController).updateSurveyFieldValues(
                              context,
                              ref,
                              '$transformerNoPrefixText${poleTransformerNoController.text.trim()}',
                              'poleTransFormerNo',
                            );
                      }
                      final concatMotorRatingValue = poleMotorRatingController
                              .text.isEmpty
                          ? '0 $selectedPoleRatingUnits'
                          : '${poleMotorRatingController.text} $selectedPoleRatingUnits';
                      ref.read(surveyController).updateSurveyFieldValues(
                            context,
                            ref,
                            concatMotorRatingValue,
                            'motorRating',
                          );
                      final generatedPoleName =
                          ref.read(surveyController).generatePoleName(
                                template: poleNoTemplate,
                                wardNo: wardNumber,
                                poleNo: 'P$poleNumber',
                                switchPointNo:
                                    'SP${switchPointController.text.trim()}',
                              );
                      log('Formatted Pole Number: $generatedPoleName');
                      ref.read(surveyController).updateSurveyFieldValues(
                            context,
                            ref,
                            generatedPoleName,
                            'poleNumber',
                          );

                      // All validations passed
                      await Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) =>
                                const CaptureThreeImagesScreen()),
                      );
                    }),
                const SizedBox(
                  height: 10,
                ),

                // Upload Status Indicator
                // FutureBuilder<Map<String, int>>(
                //   future: getSurveyUploadStats(),
                //   builder: (context, snapshot) {
                //     if (snapshot.hasData) {
                //       final stats = snapshot.data!;
                //       final total = stats['total'] ?? 0;
                //       final uploaded = stats['uploaded'] ?? 0;
                //       final pending = stats['pending'] ?? 0;

                //       if (total == 0) {
                //         return Container(); // No surveys to show
                //       }

                //       return Container(
                //         margin: const EdgeInsets.symmetric(
                //             horizontal: 20, vertical: 10),
                //         padding: const EdgeInsets.all(12),
                //         decoration: BoxDecoration(
                //           color: hasInternet
                //               ? (pending > 0
                //                   ? Colors.orange.shade100
                //                   : Colors.green.shade100)
                //               : Colors.red.shade100,
                //           borderRadius: BorderRadius.circular(12),
                //           border: Border.all(
                //             color: hasInternet
                //                 ? (pending > 0 ? Colors.orange : Colors.green)
                //                 : Colors.red,
                //             width: 1,
                //           ),
                //         ),
                //         child: Row(
                //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //           children: [
                //             Expanded(
                //               child: Column(
                //                 crossAxisAlignment: CrossAxisAlignment.start,
                //                 children: [
                //                   Text(
                //                     hasInternet
                //                         ? (pending > 0
                //                             ? 'Upload Status'
                //                             : 'All Synced')
                //                         : 'Offline Mode',
                //                     style: TextStyle(
                //                       fontWeight: FontWeight.bold,
                //                       color: hasInternet
                //                           ? (pending > 0
                //                               ? Colors.orange.shade800
                //                               : Colors.green.shade800)
                //                           : Colors.red.shade800,
                //                     ),
                //                   ),
                //                   const SizedBox(height: 4),
                //                   Text(
                //                     'Total: $total | Uploaded: $uploaded | Pending: $pending',
                //                     style: TextStyle(
                //                       fontSize: 12,
                //                       color: hasInternet
                //                           ? (pending > 0
                //                               ? Colors.orange.shade700
                //                               : Colors.green.shade700)
                //                           : Colors.red.shade700,
                //                     ),
                //                   ),
                //                 ],
                //               ),
                //             ),
                //             Icon(
                //               hasInternet
                //                   ? (pending > 0
                //                       ? Icons.cloud_upload
                //                       : Icons.cloud_done)
                //                   : Icons.cloud_off,
                //               color: hasInternet
                //                   ? (pending > 0 ? Colors.orange : Colors.green)
                //                   : Colors.red,
                //             ),
                //           ],
                //         ),
                //       );
                //     }

                //     // Fallback for when data is loading
                //     return hasInternet == false
                //         ? Container(
                //             height: 50,
                //             width: deviceWidth * 0.8,
                //             decoration: BoxDecoration(
                //                 color: const Color(0xFF666666),
                //                 borderRadius: BorderRadius.circular(16)),
                //             child: const Row(
                //               mainAxisAlignment: MainAxisAlignment.center,
                //               children: [
                //                 Icon(
                //                   Icons.cloud_off_outlined,
                //                   color: Colors.white,
                //                 ),
                //                 Text(
                //                   '  Offline!',
                //                   textAlign: TextAlign.center,
                //                   style: TextStyle(
                //                       color: Colors.white,
                //                       fontWeight: FontWeight.bold),
                //                 ),
                //               ],
                //             ))
                //         : Container();
                //   },
                // ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

Widget buildNumberInputRow(
  r.WidgetRef ref,
  BuildContext context,
  String label,
  TextEditingController controller, {
  bool enabled = true,
}) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 6),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
          ),
        ),
        SizedBox(
          width: 55,
          child: TextFormField(
            controller: controller,
            enabled: enabled,
            keyboardType: TextInputType.number,
            onChanged: enabled
                ? (val) => ref
                    .read(surveyController)
                    .updateSurveyFieldValues(context, ref, val, label)
                : null,
            style: const TextStyle(color: Colors.black),
            decoration: InputDecoration(
              isDense: true,
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(5),
              ),
            ),
          ),
        ),
      ],
    ),
  );
}

Widget lampTypesDropDown(
  BuildContext context, {
  required String selectedValue,
  required List<String> items,
  required ValueChanged<String?> onChanged,
  required bool isExpanded,
}) {
  double deviceWidth = MediaQuery.of(context).size.width;

  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 8.0),
    child: DropdownButtonHideUnderline(
      child: DropdownButton2(
        isDense: true,
        isExpanded: isExpanded,
        value: items.contains(selectedValue) ? selectedValue : null,
        items: items.map((String item) {
          return DropdownMenuItem(
            alignment: AlignmentDirectional.centerStart,
            value: item,
            child: Text(
              item,
              style: const TextStyle(color: Colors.black, fontSize: 12),
            ),
          );
        }).toList(),
        onChanged: onChanged,
        dropdownStyleData: DropdownStyleData(
          padding: const EdgeInsets.only(left: 1),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(3),
            color: Theme.of(context).primaryColor,
          ),
        ),
        buttonStyleData: ButtonStyleData(
          height: 45,
          width: deviceWidth,
          padding: const EdgeInsets.symmetric(horizontal: 10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(3),
            color: Theme.of(context).primaryColor,
          ),
          elevation: 1,
        ),
      ),
    ),
  );
}
