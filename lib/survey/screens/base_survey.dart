import 'dart:async';
import 'dart:developer';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as r;
import 'package:provider/provider.dart';
import 'package:schnell_pole_installation/Pole_Installation_Page/pole_installation.dart';
import 'package:schnell_pole_installation/Provider/provider.dart';
import 'package:schnell_pole_installation/survey/excel_export.dart';
import 'package:schnell_pole_installation/survey/survey_controller.dart';
import 'package:schnell_pole_installation/survey/background_sync_manager.dart';
import 'package:schnell_pole_installation/survey/widgets/sync_status_indicator.dart';
import 'package:schnell_pole_installation/utils/box_container.dart';
import 'package:schnell_pole_installation/utils/constants.dart';
import 'pole_survey.dart';
import 'switch_point_survey.dart';
import 'transformer_survey.dart';

class BaseSurveyInformation extends r.ConsumerStatefulWidget {
  const BaseSurveyInformation({super.key});
  @override
  r.ConsumerState<BaseSurveyInformation> createState() => _BaseSurveyState();
}

class _BaseSurveyState extends r.ConsumerState<BaseSurveyInformation>
    with WidgetsBindingObserver {
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;
  List<ConnectivityResult> _connectionStatus = [ConnectivityResult.none];
  final Connectivity _connectivity = Connectivity();
  bool hasInternet = false;
  // Background sync manager
  final BackgroundSyncManager _syncManager = BackgroundSyncManager.instance;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(surveyController).fetchNetworkDetails();
      // Check initial connectivity status
      _checkInitialConnectivity();
      // Initialize background sync manager
      _initializeBackgroundSync();
    });
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }

  /// Initialize background sync manager
  Future<void> _initializeBackgroundSync() async {
    try {
      await _syncManager.initialize();
      log('Background sync manager initialized for base survey');
    } catch (e) {
      log('Failed to initialize background sync manager: $e');
    }
  }

  /// Check initial connectivity status when screen loads
  Future<void> _checkInitialConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      await _updateConnectionStatus(result);
    } catch (e) {
      log('Error checking initial connectivity: $e');
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // App came back to foreground, check connectivity
      log('App resumed - checking connectivity status');
      _checkConnectivityOnResume();
    }
  }

  /// Check connectivity when app resumes or screen comes back into focus
  Future<void> _checkConnectivityOnResume() async {
    try {
      final result = await _connectivity.checkConnectivity();
      await _updateConnectionStatus(result);
    } catch (e) {
      log('Error checking connectivity on resume: $e');
    }
  }

  Future<void> initConnectivity() async {
    late List<ConnectivityResult> result;
    // Platform messages may fail, so we use a try/catch PlatformException.
    try {
      result = await _connectivity.checkConnectivity();
      debugPrint('Connection Status : $result');
    } on PlatformException catch (e) {
      debugPrint(
        'Couldn\'t check connectivity status : $e',
      );
      return;
    }
    if (!mounted) {
      return Future.value(null);
    }
    return _updateConnectionStatus(result);
  }

  // Debounce mechanism for connectivity changes
  static DateTime? _lastConnectivityChange;
  static const Duration _connectivityDebounceDelay = Duration(seconds: 3);

  Future<void> _updateConnectionStatus(List<ConnectivityResult> result) async {
    var dataBloc = Provider.of<DataModel>(context, listen: false);
    setState(() {
      _connectionStatus = result;
    });
    debugPrint('Update Install Connection Status : $_connectionStatus');

    if (_connectionStatus.contains(ConnectivityResult.none) ||
        _connectionStatus.isEmpty) {
      setState(() {
        hasInternet = false;
      });
      dataBloc.hasInternet = false;
      log('Internet connection lost');
    } else {
      // Debounce connectivity changes to prevent rapid upload attempts
      final now = DateTime.now();
      if (_lastConnectivityChange != null &&
          now.difference(_lastConnectivityChange!) <
              _connectivityDebounceDelay) {
        log('Connectivity change debounced - too soon since last change');
        setState(() {
          hasInternet = true;
        });
        dataBloc.hasInternet = true;
        return;
      }
      _lastConnectivityChange = now;

      log('Internet connection restored - triggering background sync');

      // Trigger background sync when internet is restored
      _syncManager.startBackgroundSync();

      setState(() {
        hasInternet = true;
      });
      dataBloc.hasInternet = true;
    }
  }

  // Survey data sync is now handled by BackgroundSyncManager
  // The old uploadSurveyData and uploadCapturedImages methods have been replaced
  // with background sync to prevent UI blocking during bulk uploads

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _connectivitySubscription.cancel();
    // Background sync manager will continue running in background
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var dataBloc = Provider.of<DataModel>(context, listen: false);
    final selectedRoadCategory =
        ref.watch(surveyController).selectedRoadCategory;
    final selectedRoadWidth = ref.watch(surveyController).selectedRoadWidth;
    final selectedVehicleAccess =
        ref.watch(surveyController).selectedVehicleAccess;
    final selectedRoadType = ref.watch(surveyController).selectedRoadType;
    final selectedTrafficSpeed =
        ref.watch(surveyController).selectedTrafficSpeed;
    final selectedTrafficDensity =
        ref.watch(surveyController).selectedTrafficDensity;
    final selectedAssetType = ref.watch(surveyController).selectedAssetType;

    return Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          backgroundColor: lightBlue,
          elevation: 0.0,
          centerTitle: true,
          titleTextStyle: TextStyle(
            color: bDarkBlue,
            fontSize: 18.0,
          ),
          title: Text(
            'Asset Survey',
            style: TextStyle(color: bDarkBlue),
          ),
          actions: [
            // Export to Excel button
            IconButton(
              onPressed: () async {
                await showExportDialog(context);
              },
              icon: const Icon(
                Icons.file_download,
                color: Colors.green,
              ),
            ),
            // Background sync status indicator
            const CompactSyncStatusIndicator(),
            const SizedBox(width: 8),
          ],
        ),
        body: WillPopScope(
            onWillPop: () async {
              Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => const PoleInstallation()),
              );
              return true;
            },
            child: SafeArea(
                child: SingleChildScrollView(
                    child: Column(mainAxisSize: MainAxisSize.min, children: [
              Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: 10.0, vertical: 20.0),
                child: GestureDetector(
                  child: BoxContainer.rectangleContainer(
                      '${dataBloc.region} > ${dataBloc.zone} > ${dataBloc.ward}'),
                  onTap: () {},
                ),
              ),
              Row(
                children: [
                  Expanded(
                      child: BoxContainer.buildDropdown(
                    context,
                    value: selectedRoadCategory,
                    label: "Existing Road Category",
                    items: roadTypeOptions.keys.toList(),
                    onChanged: (value) async {
                      ref
                          .read(surveyController)
                          .updateRoadCategoryCascade(context, ref, value!);
                    },
                  )),
                  Expanded(
                      child: BoxContainer.buildDropdown(
                    context,
                    value: selectedRoadType,
                    label: "Road Type",
                    items: (roadTypeOptions[selectedRoadCategory] ?? [])
                        .cast<String>(),
                    onChanged: (value) async {
                      ref
                          .read(surveyController)
                          .updateRoadTypeCascade(context, ref, value!);
                    },
                  )),
                ],
              ),
              const SizedBox(
                height: 10,
              ),
              Row(
                children: [
                  Expanded(
                      child: BoxContainer.buildDropdown(
                    context,
                    value: selectedRoadWidth,
                    label: "Road Width",
                    items: (roadWidthOptions[selectedRoadType] ?? [])
                        .cast<String>(),
                    onChanged: (value) async {
                      ref
                          .read(surveyController)
                          .updateRoadWidthCascade(context, ref, value!);
                    },
                  )),
                  Expanded(
                      child: BoxContainer.buildDropdown(
                    context,
                    value: selectedVehicleAccess,
                    label: "Vehicle Access",
                    items: (vehicleAccessOptions[selectedRoadWidth] ?? [])
                        .cast<String>(),
                    onChanged: (value) async {
                      if (value != null) {
                        ref.read(surveyController).updateVehicleAccess(value);
                      }
                    },
                  )),
                ],
              ),
              Row(
                children: [
                  Expanded(
                      child: BoxContainer.buildDropdown(
                    context,
                    value: selectedTrafficDensity,
                    label: "Traffic Density",
                    items: trafficDensityOptions,
                    onChanged: (value) async {
                      log('selected value $value');

                      ref.read(surveyController).updateSurveyFieldValues(
                          context, ref, value, 'trafficDensity');
                    },
                  )),
                  Expanded(
                      child: BoxContainer.buildDropdown(
                    context,
                    value: selectedTrafficSpeed,
                    label: "Traffic Speed",
                    items: trafficSpeedOptions,
                    onChanged: (value) async {
                      log('selected value $value');

                      ref.read(surveyController).updateSurveyFieldValues(
                          context, ref, value, 'trafficSpeed');
                    },
                  )),
                ],
              ),
              // Padding(
              //   padding:
              //       const EdgeInsets.symmetric(horizontal: 10.0, vertical: 6.0),
              //   child: TextField(
              //     readOnly: true,
              //     maxLength: 4,
              //     // controller: carrierNameController,
              //     obscureText: false,
              //     style: const TextStyle(
              //       color: Colors.black,
              //       fontSize: 14,
              //       overflow: TextOverflow.ellipsis,
              //     ),
              //     keyboardType: TextInputType.number,
              //     decoration: InputDecoration(
              //       counterText: '',
              //       suffixIcon: SizedBox(
              //         width: 55,
              //         child: Container(
              //           margin:
              //               const EdgeInsets.only(right: 15, top: 5, bottom: 5),
              //           padding: const EdgeInsets.symmetric(
              //               horizontal: 8, vertical: 4),
              //           decoration: BoxDecoration(
              //             color: Colors.blue.shade100,
              //             borderRadius: BorderRadius.circular(50),
              //           ),
              //           child: Center(
              //             child: Text(
              //               signalStrengthLevel.toString(),
              //               style: const TextStyle(
              //                 fontWeight: FontWeight.bold,
              //                 fontSize: 14,
              //                 color: Colors.blueAccent,
              //               ),
              //             ),
              //           ),
              //         ),
              //       ),
              //       border: OutlineInputBorder(
              //         borderRadius: BorderRadius.circular(8),
              //       ),
              //       hintText: carrierName,
              //       hintStyle: const TextStyle(
              //         color: Colors.black,
              //         fontSize: 14,
              //         fontWeight: FontWeight.w500,
              //       ),
              //       labelText: 'Signal Strength',
              //       labelStyle: TextStyle(
              //           fontSize: 17,
              //           color: darkBlue,
              //           fontWeight: FontWeight.bold),
              //       floatingLabelBehavior: FloatingLabelBehavior.always,
              //     ),
              //   ),
              // ),
              // const SizedBox(
              //   height: 10,
              // ),
              Row(
                children: [
                  Expanded(
                    child: BoxContainer.buildDropdown(
                      context,
                      value: selectedAssetType,
                      label: "Asset Type",
                      items: assetTypes,
                      onChanged: (newValue) {
                        log('selected value $newValue');

                        ref.read(surveyController).updateSurveyFieldValues(
                            context, ref, newValue, 'assetType');
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: 30,
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10.0, vertical: 6.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    // Expanded(
                    //   child: GestureDetector(
                    //     onTap: () {
                    //       Navigator.push(
                    //         context,
                    //         MaterialPageRoute(
                    //             builder: (context) => const PoleInstallation()),
                    //       );
                    //     },
                    //     child: Container(
                    //       height: 40,
                    //       // width: width / 2.50,
                    //       decoration: BoxDecoration(
                    //           border: Border.all(
                    //               width: 0.9, color: Colors.blueGrey),
                    //           borderRadius: BorderRadius.circular(8)),
                    //       child: const Center(
                    //         child: Text(
                    //           'CANCEL',
                    //           style: TextStyle(color: Colors.black),
                    //         ),
                    //       ),
                    //     ),
                    //   ),
                    // ),
                    const SizedBox(
                      width: 10,
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          EasyLoading.show(
                            status: '',
                            dismissOnTap: false,
                          );
                          if (selectedAssetType == 'Switch Point') {
                            EasyLoading.dismiss();
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      const SwitchPointSurvey()),
                            );
                          } else if (selectedAssetType == 'Pole') {
                            ref
                                .read(surveyController)
                                .updatePoleHeightCascade(selectedRoadType);
                            EasyLoading.dismiss();

                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      const PoleSurveyInformation()),
                            );
                          } else if (selectedAssetType == 'Transformer') {
                            EasyLoading.dismiss();

                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      const TransformerSurvey()),
                            );
                          } else {
                            EasyLoading.dismiss();
                          }
                        },
                        child: Container(
                          height: 40,
                          // width: width / 2.50,
                          decoration: BoxDecoration(
                              color: const Color.fromARGB(248, 64, 124, 161)
                                  .withOpacity(0.8),
                              borderRadius: BorderRadius.circular(8)),
                          child: const Center(
                            child: Text(
                              'Proceed',
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ])))));
  }
}
