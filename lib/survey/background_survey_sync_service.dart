import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'dart:isolate';
import 'package:flutter/services.dart';
import 'package:geocoding/geocoding.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:schnell_pole_installation/survey/survey_service.dart';
import 'package:schnell_pole_installation/survey/s3_upload_service.dart';
import 'package:schnell_pole_installation/survey/models/survey_image_model.dart';
import 'package:schnell_pole_installation/utils/utility.dart';

/// Background sync service for survey data
/// Handles survey data synchronization in background isolates to prevent UI blocking
class BackgroundSurveySyncService {
  static BackgroundSurveySyncService? _instance;
  static BackgroundSurveySyncService get instance =>
      _instance ??= BackgroundSurveySyncService._();

  BackgroundSurveySyncService._();

  /// Reset the singleton instance (useful for testing or when app needs fresh start)
  static void resetInstance() {
    _instance?.dispose();
    _instance = null;
  }

  // Isolate management
  Isolate? _syncIsolate;
  SendPort? _syncSendPort;
  ReceivePort? _syncReceivePort;
  StreamSubscription? _receivePortSubscription;

  // Sync state management
  bool _isSyncing = false;
  bool _isInitialized = false;

  // Track initialization to prevent multiple attempts
  Future<void>? _initializationFuture;

  // Progress tracking
  final StreamController<SyncProgress> _progressController =
      StreamController<SyncProgress>.broadcast();
  Stream<SyncProgress> get progressStream => _progressController.stream;

  // Sync status tracking
  final StreamController<SyncStatus> _statusController =
      StreamController<SyncStatus>.broadcast();
  Stream<SyncStatus> get statusStream => _statusController.stream;

  /// Initialize the background sync service
  Future<void> initialize() async {
    // If already initialized, return immediately
    if (_isInitialized) return;

    // If initialization is in progress, wait for it to complete
    if (_initializationFuture != null) {
      return _initializationFuture!;
    }

    // Start initialization
    _initializationFuture = _performInitialization();

    try {
      await _initializationFuture!;
    } finally {
      _initializationFuture = null;
    }
  }

  /// Perform the actual initialization
  Future<void> _performInitialization() async {
    try {
      log('Initializing BackgroundSurveySyncService...');

      // Cleanup any existing resources first
      _cleanup();

      // Check if we're in a test environment
      final rootIsolateToken = RootIsolateToken.instance;
      final isTestEnvironment = rootIsolateToken == null;

      if (isTestEnvironment) {
        log('Detected test environment - skipping isolate initialization');
        _isInitialized = true;
        return;
      }

      // Get the Hive path from the main isolate to ensure consistency
      String hivePath;
      try {
        final appDocDir = await getApplicationDocumentsDirectory();
        hivePath = appDocDir.path;
        log('Main thread: Got Hive path for isolate: $hivePath');
      } catch (e) {
        log('Failed to get application documents directory: $e');
        // Fallback to a default path
        hivePath = '/tmp/hive_isolate';
        log('Main thread: Using fallback Hive path: $hivePath');
      }

      // Create receive port for communication with isolate
      _syncReceivePort = ReceivePort();

      // Wait for isolate to send back its SendPort
      final Completer<SendPort> completer = Completer<SendPort>();
      bool sendPortReceived = false;

      // Set up the main message listener that handles both SendPort and regular messages
      _receivePortSubscription = _syncReceivePort!.listen((message) {
        if (message is SendPort && !sendPortReceived) {
          _syncSendPort = message;
          sendPortReceived = true;
          completer.complete(message);
        } else {
          // Handle regular isolate messages
          _handleIsolateMessage(message);
        }
      });

      // Spawn the sync isolate with the root isolate token and Hive path
      _syncIsolate = await Isolate.spawn(
        _syncIsolateEntryPoint,
        {
          'sendPort': _syncReceivePort!.sendPort,
          'rootIsolateToken': rootIsolateToken,
          'hivePath': hivePath,
        },
      );

      await completer.future.timeout(const Duration(seconds: 5));

      _isInitialized = true;
      log('BackgroundSurveySyncService initialized successfully');
    } catch (e) {
      log('Failed to initialize BackgroundSurveySyncService: $e');
      _cleanup();
      rethrow;
    }
  }

  /// Start background sync for survey data
  Future<void> startSurveySync() async {
    if (!_isInitialized) {
      await initialize();
    }

    if (_isSyncing) {
      log('Survey sync already in progress, skipping...');
      return;
    }

    log('BackgroundSurveySyncService: startSurveySync() called at ${DateTime.now()}');

    try {
      _isSyncing = true;
      _statusController.add(SyncStatus.syncing);

      log('Starting background survey sync...');

      // Check if we're in test environment (no isolate)
      if (_syncSendPort == null) {
        log('No isolate available (test environment) - simulating sync completion');
        _isSyncing = false;
        _statusController.add(SyncStatus.completed);
        return;
      }

      // Send sync command to isolate
      _syncSendPort?.send({
        'command': 'start_survey_sync',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      log('Error starting survey sync: $e');
      _isSyncing = false;
      _statusController.add(SyncStatus.error);
    }
  }

  /// Start background sync for captured images
  Future<void> startImageSync() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      log('Starting background image sync...');

      // Check if we're in test environment (no isolate)
      if (_syncSendPort == null) {
        log('No isolate available (test environment) - simulating image sync completion');
        _statusController.add(SyncStatus.completed);
        return;
      }

      // Send image sync command to isolate
      _syncSendPort?.send({
        'command': 'start_image_sync',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      log('Error starting image sync: $e');
    }
  }

  /// Handle messages from the sync isolate
  void _handleIsolateMessage(dynamic message) {
    try {
      if (message is Map<String, dynamic>) {
        final String type = message['type'] ?? '';

        switch (type) {
          case 'progress':
            final progress = SyncProgress.fromMap(message);
            _progressController.add(progress);
            break;

          case 'status':
            final status = SyncStatus.values.firstWhere(
              (s) => s.name == message['status'],
              orElse: () => SyncStatus.idle,
            );
            _statusController.add(status);

            if (status == SyncStatus.completed || status == SyncStatus.error) {
              _isSyncing = false;
            }
            break;

          case 'log':
            log('Isolate: ${message['message']}');
            break;

          case 'error':
            log('Isolate Error: ${message['error']}');
            _isSyncing = false;
            _statusController.add(SyncStatus.error);
            break;
        }
      }
    } catch (e) {
      log('Error handling isolate message: $e');
    }
  }

  /// Cleanup resources
  void _cleanup() {
    _syncIsolate?.kill(priority: Isolate.immediate);
    _syncIsolate = null;
    _receivePortSubscription?.cancel();
    _receivePortSubscription = null;
    _syncReceivePort?.close();
    _syncReceivePort = null;
    _syncSendPort = null;
    _isInitialized = false;
    _isSyncing = false;
  }

  /// Dispose the service
  void dispose() {
    _cleanup();
    _progressController.close();
    _statusController.close();
  }

  /// Get current sync status
  bool get isSyncing => _isSyncing;
  bool get isInitialized => _isInitialized;
}

/// Entry point for the sync isolate
void _syncIsolateEntryPoint(Map<String, dynamic> params) async {
  final SendPort mainSendPort = params['sendPort'] as SendPort;
  final RootIsolateToken? rootIsolateToken =
      params['rootIsolateToken'] as RootIsolateToken?;
  final String? hivePath = params['hivePath'] as String?;

  // Send debug info immediately
  mainSendPort.send({
    'type': 'log',
    'message': 'Isolate started with hivePath: $hivePath',
  });

  try {
    // Initialize the background isolate binary messenger
    // This is required for using Flutter services in background isolates
    if (rootIsolateToken != null) {
      BackgroundIsolateBinaryMessenger.ensureInitialized(rootIsolateToken);
      mainSendPort.send({
        'type': 'log',
        'message': 'BackgroundIsolateBinaryMessenger initialized successfully',
      });
    } else {
      // In test environments, RootIsolateToken may be null
      // We'll continue without BackgroundIsolateBinaryMessenger initialization
      // This means HTTP requests and some Flutter services won't work in the isolate
      mainSendPort.send({
        'type': 'log',
        'message':
            'Warning: RootIsolateToken is null - running in test/limited mode',
      });
    }

    // Initialize Hive in the isolate using the same path as the main isolate
    // This ensures that the isolate can access the same Hive boxes as the main app
    try {
      if (hivePath != null && hivePath.isNotEmpty) {
        mainSendPort.send({
          'type': 'log',
          'message': 'Initializing Hive in isolate with path: $hivePath',
        });

        // Ensure clean Hive state in isolate
        try {
          // Close specific boxes if they are open
          final boxesToCheck = ['surveyBox', 'imagesBox'];
          for (final boxName in boxesToCheck) {
            if (Hive.isBoxOpen(boxName)) {
              mainSendPort.send({
                'type': 'log',
                'message': 'Closing existing $boxName in isolate',
              });
              await Hive.box(boxName).close();
            }
          }
        } catch (e) {
          mainSendPort.send({
            'type': 'log',
            'message': 'Error closing existing boxes: $e',
          });
        }

        Hive.init(hivePath);
        mainSendPort.send({
          'type': 'log',
          'message':
              'Hive initialized successfully in isolate with path: $hivePath',
        });

        // Verify that we can see the same files as the main thread
        try {
          final dir = Directory(hivePath);
          final files =
              dir.listSync().where((f) => f.path.contains('hive')).toList();
          mainSendPort.send({
            'type': 'log',
            'message':
                'Hive files in isolate directory: ${files.map((f) => f.path.split('/').last).toList()}',
          });
        } catch (e) {
          mainSendPort.send({
            'type': 'log',
            'message': 'Could not list Hive files in isolate: $e',
          });
        }
      } else {
        // Fallback: try to get the path in the isolate
        final appDocDir = await getApplicationDocumentsDirectory();
        mainSendPort.send({
          'type': 'log',
          'message':
              'Fallback: Initializing Hive in isolate with path: ${appDocDir.path}',
        });
        Hive.init(appDocDir.path);
        mainSendPort.send({
          'type': 'log',
          'message':
              'Hive initialized successfully in isolate (fallback) with path: ${appDocDir.path}',
        });
      }
    } catch (e) {
      // Last resort fallback to a simple path if everything fails
      mainSendPort.send({
        'type': 'log',
        'message':
            'Error initializing Hive in isolate: $e, using fallback path',
      });
      Hive.init('/tmp/hive_isolate');
      mainSendPort.send({
        'type': 'log',
        'message':
            'Hive initialized with fallback path in isolate: /tmp/hive_isolate',
      });
    }

    // Create receive port for this isolate
    final isolateReceivePort = ReceivePort();

    // Send the isolate's send port back to main isolate
    mainSendPort.send(isolateReceivePort.sendPort);

    // Create sync worker
    final worker = _SyncWorker(mainSendPort);

    // Listen for commands from main isolate
    await for (final message in isolateReceivePort) {
      if (message is Map<String, dynamic>) {
        final String command = message['command'] ?? '';

        switch (command) {
          case 'start_survey_sync':
            await worker.syncSurveyData();
            break;

          case 'start_image_sync':
            await worker.syncImages();
            break;

          case 'stop':
            isolateReceivePort.close();
            return;
        }
      }
    }
  } catch (e) {
    // Send error back to main isolate
    mainSendPort.send({
      'type': 'error',
      'error': 'Isolate initialization failed: $e',
    });
  }
}

/// Worker class that handles the actual sync operations in the isolate
class _SyncWorker {
  final SendPort _mainSendPort;
  final SurveyService _surveyService = SurveyService();

  _SyncWorker(this._mainSendPort);

  /// Send message to main isolate
  void _sendMessage(Map<String, dynamic> message) {
    try {
      _mainSendPort.send(message);
    } catch (e) {
      // Ignore send errors in isolate
    }
  }

  /// Send log message
  void _log(String message) {
    _sendMessage({
      'type': 'log',
      'message': message,
    });
  }

  /// Send error message
  void _sendError(String error) {
    _sendMessage({
      'type': 'error',
      'error': error,
    });
  }

  /// Send progress update
  void _sendProgress(int current, int total, String operation) {
    _sendMessage({
      'type': 'progress',
      'current': current,
      'total': total,
      'operation': operation,
      'percentage': total > 0 ? (current / total * 100).round() : 0,
    });
  }

  /// Send status update
  void _sendStatus(SyncStatus status) {
    _sendMessage({
      'type': 'status',
      'status': status.name,
    });
  }

  /// Sync survey data in background
  Future<void> syncSurveyData() async {
    try {
      _sendStatus(SyncStatus.syncing);
      _log('Starting survey data sync...');

      // Small delay to ensure main thread has finished any Hive operations
      await Future.delayed(const Duration(milliseconds: 200));
      _log('Waited for main thread operations to complete');

      // Check internet connectivity
      bool isOnline = false;
      try {
        isOnline = await Utility.isConnected();
      } catch (e) {
        _log('Failed to check connectivity in isolate: $e');
        _sendStatus(SyncStatus.error);
        return;
      }

      if (!isOnline) {
        _log('No internet connection available for survey upload');
        _sendStatus(SyncStatus.error);
        return;
      }

      // Open survey box with proper error handling for concurrent access
      _log('Attempting to open surveyBox in isolate...');
      Box? box;
      int totalRecords = 0;

      try {
        // Simple approach: just open the box directly
        _log('Opening surveyBox in isolate...');
        box = await Hive.openBox('surveyBox');

        totalRecords = box.keys.length;
        _log('Successfully opened surveyBox with $totalRecords records');

        // Debug: Check if this is the same database by looking for specific keys
        if (totalRecords > 0) {
          _log('Found keys in surveyBox: ${box.keys.toList()}');
          // Check each record to see if it matches what main thread saved
          for (var key in box.keys) {
            final record = box.get(key);
            if (record != null && record is Map) {
              _log(
                  'Record $key: assetType=${record['assetType']}, isUploaded=${record['isUploaded']}');
            }
          }
        } else {
          // If no records found, let's verify we can write to this box
          _log('No records found. Testing if we can write to this box...');
          try {
            final testKey = await box.add({
              'test': 'isolate_test',
              'timestamp': DateTime.now().millisecondsSinceEpoch
            });
            _log('Successfully wrote test record with key: $testKey');
            await box.delete(testKey);
            _log('Successfully deleted test record');
          } catch (e) {
            _log('Error writing test record: $e');
          }
        }
      } catch (e) {
        _log('Error opening surveyBox: $e');
        _sendStatus(SyncStatus.error);
        return;
      }

      // Debug: Log all keys and some sample data
      _log('Survey box keys: ${box.keys.toList()}');
      if (totalRecords > 0) {
        final firstRecord = box.getAt(0);
        _log(
            'First record sample: ${firstRecord?.toString().substring(0, 100)}...');
      } else {
        // If no records found, let's verify we can write to this box
        _log('No records found. Testing if we can write to this box...');
        try {
          final testKey = await box.add({
            'test': 'isolate_test',
            'timestamp': DateTime.now().millisecondsSinceEpoch
          });
          _log('Successfully wrote test record with key: $testKey');
          await box.delete(testKey);
          _log('Successfully deleted test record');
        } catch (e) {
          _log('Error writing test record: $e');
        }
      }

      if (totalRecords == 0) {
        _log('No survey records to sync');
        _sendStatus(SyncStatus.completed);
        return;
      }

      _log('Found $totalRecords survey records to sync');
      _sendProgress(0, totalRecords, 'Syncing survey data');

      int uploadedCount = 0;
      int alreadyUploadedCount = 0;
      int skippedCount = 0;

      // Process each survey record
      for (int i = 0; i < box.keys.length; i++) {
        final key = box.keys.elementAt(i);
        final keyString = key.toString();

        try {
          final data = box.get(key);
          if (data == null) continue;

          final Map<String, dynamic> surveyMap =
              Map<String, dynamic>.from(data);

          // Check if already uploaded
          final bool isUploaded = surveyMap['isUploaded'] ?? false;
          if (isUploaded) {
            alreadyUploadedCount++;
            _sendProgress(i + 1, totalRecords, 'Syncing survey data');
            continue;
          }

          // Determine survey type and sync accordingly
          final String assetType = surveyMap['assetType'] ?? '';
          bool syncResult = false;
          _log('asset type in backaground thread: $assetType');
          syncResult = await _syncSurvey(surveyMap);
          // switch (assetType) {
          //   case 'Pole':
          //     syncResult = await _syncPoleSurvey(surveyMap);
          //     break;
          //   case 'Switch Point':
          //     syncResult = await _syncSwitchPointSurvey(surveyMap);
          //     break;
          //   case 'Transformer':
          //     syncResult = await _syncTransformerSurvey(surveyMap);
          //     break;
          //   default:
          //     _log('Unknown asset type: $assetType');
          //     continue;
          // }

          if (syncResult) {
            // Mark as uploaded
            surveyMap['isUploaded'] = true;
            await box.put(key, surveyMap);
            uploadedCount++;
            _log('Successfully synced $assetType survey');
          } else {
            _log('Failed to sync $assetType survey');
          }

          _sendProgress(i + 1, totalRecords, 'Syncing survey data');
        } catch (e) {
          _log('Error processing survey record $keyString: $e');
          skippedCount++;
        }
      }

      _log(
          'Survey sync completed. Uploaded: $uploadedCount, Already uploaded: $alreadyUploadedCount, Skipped: $skippedCount');
      _sendStatus(SyncStatus.completed);
    } catch (e) {
      _sendError('Survey sync failed: $e');
      _sendStatus(SyncStatus.error);
    }
  }

  // /// Sync pole survey data
  // Future<bool> _syncPoleSurvey(Map<String, dynamic> surveyData) async {
  //   _log('${jsonEncode(surveyData)} ready to upload');
  //   try {
  //     final result = await _surveyService.updateSurveyDetailService(surveyData);
  //     return result == '200' || result == 'success';
  //   } catch (e) {
  //     _log('Error syncing pole survey: $e');
  //     return false;
  //   }
  // }

  Future<String> getLocation(lat, long) async {
    List<Placemark> placemarks = await placemarkFromCoordinates(lat, long);
    Placemark place = placemarks[0];
    var locationData =
        '${place.subLocality}, ${place.locality}, ${place.administrativeArea}, ${place.country}, ${place.postalCode}';
    // location = '${place.name},${place.street},${place.subLocality}, ${place.locality},${place.postalCode},';
    return locationData;
  }

  Future<bool> _syncSurvey(data) async {
    var locationCheck = data['location'];
    var manualEnteredLocation = data['manualEnteredLocation'];
    String? filteredLandMark;
    if (locationCheck == null) {
      String landMark = await getLocation(data['latitude'], data['longitude']);
      String? concatenatedLocation = '$manualEnteredLocation,$landMark';
      filteredLandMark = concatenatedLocation
          .replaceAll(RegExp(r',\s*'), ',')
          .replaceAll(RegExp(r'^,\s*'), '')
          .trim();
    }

    final isPole = data['assetType'] == 'Pole';
    final isSwitchPoint = data['assetType'] == 'Switch Point';
    final isTransformer = data['assetType'] == 'Transformer';
    final installedOn = int.tryParse(data['installedOn'] ?? '0') ?? 0;
    final armCount = int.tryParse(data['armCount'] ?? '0') ?? 0;

    final Map<String, dynamic> postData = {
      "roadType": data['roadType'],
      "trafficDensity": data['trafficDensity'],
      "trafficSpeed": data['trafficSpeed'],
      "assetType": data['assetType'],
      "customerId": data['customerId'],
      "wardId": data['wardId'],
      if (data['comments'] != '') "remarks": data['comments'],
      "region": data['region'],
      "zoneName": data['zone'],
      "wardName": data['ward'],
      "installedBy": data['installedBy'],
      "installedOn": installedOn,
      "state": 'INSTALLED',
      "latitude": data['latitude'],
      "longitude": data['longitude'],
      "location": filteredLandMark,
      "accuracy": double.parse(data['accuracy'] == '' ? '0' : data['accuracy']),
      "altitude": data['altitude'],
      "existingRoadCategory": data['roadCategory'],
      "roadWidth": data['roadWidth'],
      "vehicleAccess": data['vehicleAccess'],
      "signalStrength": {
        data["carrierName"]: data['signalStrengthLevel'],
      },
      "auditImg": [
        if (data['uuidFileName1'] != '') data['uuidFileName1'],
        if (data['uuidFileName2'] != '') data['uuidFileName2'],
        if (data['uuidFileName3'] != '') data['uuidFileName3'],
      ]
    };

    if (isPole) {
      postData.addAll({
        "name": data['poleNumber'],
        "condition": data['poleCondition'],
      });

      if (data['switchPointNo'] != '') {
        postData["switchPointNo"] = data['switchPointNo'];
      }
      if (data['escomPoleNumber'] != '') {
        postData["ebPoleNo"] = data['escomPoleNumber'];
      }
      if (data['exCorpPoleNo'] != '') {
        postData["exCorpPoleNo"] = data['exCorpPoleNo'];
      }
      if (data['poleTransformerNo'] != '') {
        postData["transformerNo"] = data['poleTransformerNo'];
      }
      if (data['poleCondition'] != 'Missing') {
        postData.addAll({
          "type": data['poleType'],
          "height": data['poleHeight'],
          "earthingRequired": data['earthingRequired'],
          "manualSwitchControl": data['manualSwitchControl'],
          "incomingTransmissionLine": data['incomingTransLine'],
          "incomingTransmissionType": data['incomingTransType'],
          "armCount": armCount,
          "armDetails": {
            "count": armCount,
            "length": data['armLength'],
            "condition": [
              {"condition": "Good", "count": data['goodArmCount']},
              {"condition": "Bad", "count": data['badArmCount']},
              {"condition": "Missing", "count": data['missingArmCount']},
            ]
          },
        });
        if (armCount > 0) {
          if (data['selectedLamps'].length > 0) {
            postData['lampProfiles'] = data['selectedLamps'];
          }
          postData['lightDetails'] = [
            {
              "condition": "Working",
              "count": data['workingCount'],
            },
            {
              "condition": "Not Working",
              "count": data['notWorkingCount'],
            },
            {
              "condition": "Missing",
              "count": data['missingCount'],
            },
          ];
        }

        if (data['poleType'] == 'High Mast(HM)') {
          postData['motorCondition'] = data['motorCondition'];
          if (data['motorCondition'] != 'Missing') {
            postData["motorDetails"] = {
              'rating': data['motorRating'],
              'make': data['motorMake'],
              'model': data['motorModel']
            };
            postData['winchCondition'] = data['winchCondition'];
            postData['ropeCondition'] = data['ropeCondition'];
          }
        }
        if (data['poleType'] != 'High Mast(HM)' &&
            data['poleType'] != 'Mini Mast(MM)') {
          postData["span"] = data['poleSpan'];
          postData["bracketMountingHeight"] = data['bracketMountingHeight'];
          if (data['clampType'] != 'Clamp Type Not Required') {
            postData["clampDimension"] = {
              "length": data['clampTypeLength'],
              "width": data['clampTypeWidth'],
              "unit": data['clampTypeUnits'],
            };
          }
        }
      }
    }

    if (isSwitchPoint) {
      postData.addAll({
        "switchPointNumber": data['spNo'],
        "switchPointType": data['spType'],
        "connectedLoad": (data['spconnectedLoad']?.isNotEmpty ?? false)
            ? '${data['spconnectedLoad']} kW'
            : '0 kW',
        "condition": data['spCondition'],
        "earthingCondition": data['spEarthingCondition'],
      });

      if (data['rrNo'] != '') postData["rrNumber"] = data['rrNo'];
      if (data['spId'] != '') postData["panelId"] = data['spId'];
      if (data['spTransformerNo'] != '') {
        postData["transformerNo"] = data['spTransformerNo'];
      }

      if (data['spMeter'] == 'DP/Manual') {
        postData["meterDetails"] = {
          "type": data['spMeterType'],
        };
      } else {
        postData["meterDetails"] = {
          "no": data['spMeterNo'],
          "type": data['spMeterType'],
          "make": data['spMeterMake'],
          "phase": data['spMeterPhase'],
          "status": data['spMeter'],
        };
      }
    }

    if (isTransformer) {
      postData.addAll({
        "transformerNumber": data['transformerNo'],
        "capacity": (data['transCapacity'].isNotEmpty ?? false)
            ? '${data['transCapacity']} kW'
            : '0 kW',
      });
    }

    var result = await _surveyService.updateSurveyDetailService(postData);
    if (result == "200") {
      log('$postData survey details uploaded successfully');
      // final prefs = await SharedPreferences.getInstance();
      // bool isLocationTrackingRequi =
      //     prefs.getBool('isLocationTrackingRquired') ?? false;

      // if (isLocationTrackingRequi) {
      //   String activityName = isPole
      //       ? 'Pole Survey'
      //       : isSwitchPoint
      //           ? 'Switch Point Survey'
      //           : isTransformer
      //               ? 'Transformer Survey'
      //               : '';
      //   String entityName = '';
      //   if (isPole) {
      //     entityName = postData['name'];
      //     log(entityName);
      //   } else if (isSwitchPoint) {
      //     entityName = postData['switchPointNumber'];
      //   } else if (isTransformer) {
      //     entityName = postData['transformerNumber'];
      //     log(entityName);
      //   }
      //   _poleDetailController.activityLocationTracking(
      //       activityName, entityName);
      // }
      return true;
    } else if (result == "400") {
      return true;
    }
    return false;
  }

  // /// Sync switch point survey data
  // Future<bool> _syncSwitchPointSurvey(Map<String, dynamic> surveyData) async {
  //   try {
  //     final result = await _surveyService.updateSurveyDetailService(surveyData);
  //     return result == '200' || result == 'success';
  //   } catch (e) {
  //     _log('Error syncing switch point survey: $e');
  //     return false;
  //   }
  // }

  // /// Sync transformer survey data
  // Future<bool> _syncTransformerSurvey(Map<String, dynamic> surveyData) async {
  //   try {
  //     final result =
  //         await _surveyService.updateSurveyDetailService(null, surveyData);
  //     return result == '200' || result == 'success';
  //   } catch (e) {
  //     _log('Error syncing transformer survey: $e');
  //     return false;
  //   }
  // }

  /// Sync captured images in background
  Future<void> syncImages() async {
    try {
      _sendStatus(SyncStatus.syncing);
      _log('Starting image sync...');

      // Small delay to ensure main thread has finished any Hive operations
      await Future.delayed(const Duration(milliseconds: 200));
      _log('Waited for main thread operations to complete');

      // Check internet connectivity
      bool isOnline = false;
      try {
        isOnline = await Utility.isConnected();
      } catch (e) {
        _log('Failed to check connectivity in isolate: $e');
        _sendStatus(SyncStatus.error);
        return;
      }

      if (!isOnline) {
        _log('No internet connection available for image upload');
        _sendStatus(SyncStatus.error);
        return;
      }

      // Open captured images box with proper error handling for concurrent access
      _log('Attempting to open imagesBox in isolate...');
      Box? box;
      int totalRecords = 0;

      try {
        // Simple approach: just open the box directly
        _log('Opening imagesBox in isolate...');
        box = await Hive.openBox('imagesBox');

        totalRecords = box.keys.length;
        _log('Successfully opened imagesBox with $totalRecords records');
      } catch (e) {
        _log('Error opening imagesBox: $e');
        _sendStatus(SyncStatus.error);
        return;
      }

      if (totalRecords == 0) {
        _log('No images to sync');
        _sendStatus(SyncStatus.completed);
        return;
      }

      _log('Found $totalRecords image records to sync');
      _sendProgress(0, totalRecords, 'Syncing images');

      int uploadedCount = 0;
      int alreadyUploadedCount = 0;

      // Process each image record
      for (int i = 0; i < box.keys.length; i++) {
        final key = box.keys.elementAt(i);

        try {
          final data = box.get(key);
          if (data == null) continue;

          final Map<String, dynamic> imageMap = Map<String, dynamic>.from(data);
          final model = MultiCapturedImageModel.fromMap(imageMap);

          bool updated = false;

          // Upload image 1
          if (!model.isUploaded1 && model.fileName1.isNotEmpty) {
            final result =
                await updateSurveyImage(model.base64Image1, model.fileName1);
            if (result) {
              model.isUploaded1 = true;
              updated = true;
              _log('Image 1 uploaded: ${model.fileName1}');
            }
          }

          // Upload image 2
          if (!model.isUploaded2 && model.fileName2.isNotEmpty) {
            final result =
                await updateSurveyImage(model.base64Image2, model.fileName2);
            if (result) {
              model.isUploaded2 = true;
              updated = true;
              _log('Image 2 uploaded: ${model.fileName2}');
            }
          }

          // Upload image 3
          if (!model.isUploaded3 && model.fileName3.isNotEmpty) {
            final result =
                await updateSurveyImage(model.base64Image3, model.fileName3);
            if (result) {
              model.isUploaded3 = true;
              updated = true;
              _log('Image 3 uploaded: ${model.fileName3}');
            }
          }

          if (updated) {
            await box.put(key, model.toMap());
            uploadedCount++;
          } else {
            alreadyUploadedCount++;
          }

          _sendProgress(i + 1, totalRecords, 'Syncing images');
        } catch (e) {
          _log('Error processing image record: $e');
        }
      }

      _log(
          'Image sync completed. Uploaded: $uploadedCount, Already uploaded: $alreadyUploadedCount');
      _sendStatus(SyncStatus.completed);
    } catch (e) {
      _sendError('Image sync failed: $e');
      _sendStatus(SyncStatus.error);
    }
  }

  // /// Upload image to S3 using the static method
  // Future<bool> _uploadImageToS3(String base64Image, String fileName) async {
  //   try {
  //     final result = await S3UploadService.uploadImageToS3(
  //       base64Image: base64Image,
  //       fileName: fileName,
  //       folder: 'luminator', // Use luminator folder from S3Config
  //       contentType: 'image/jpeg',
  //     );
  //     return result != null && result.isNotEmpty;
  //   } catch (e) {
  //     _log('Error uploading image $fileName: $e');
  //     return false;
  //   }
  // }

  Future<bool> updateSurveyImage(deviceImage, filename) async {
    var result = await S3UploadService()
        .updateSurveyImageServiceS3(deviceImage, filename);

    if (result == "200") {
      return true;
    } else if (result == "400") {
      return false;
    }
    return false;
  }
}

/// Sync progress data class
class SyncProgress {
  final int current;
  final int total;
  final String operation;
  final int percentage;

  SyncProgress({
    required this.current,
    required this.total,
    required this.operation,
    required this.percentage,
  });

  factory SyncProgress.fromMap(Map<String, dynamic> map) {
    return SyncProgress(
      current: map['current'] ?? 0,
      total: map['total'] ?? 0,
      operation: map['operation'] ?? '',
      percentage: map['percentage'] ?? 0,
    );
  }
}

/// Sync status enumeration
enum SyncStatus {
  idle,
  syncing,
  completed,
  error,
}
