import 'dart:async';
import 'package:flutter/material.dart';
import 'package:schnell_pole_installation/survey/background_sync_manager.dart';
import 'package:schnell_pole_installation/survey/background_survey_sync_service.dart';

/// A widget that displays the current sync status and progress
/// Shows sync progress without blocking the main UI thread
class SyncStatusIndicator extends StatefulWidget {
  final bool showProgress;
  final bool showText;
  final double iconSize;
  final EdgeInsets padding;

  const SyncStatusIndicator({
    super.key,
    this.showProgress = true,
    this.showText = true,
    this.iconSize = 24.0,
    this.padding = const EdgeInsets.all(8.0),
  });

  @override
  State<SyncStatusIndicator> createState() => _SyncStatusIndicatorState();
}

class _SyncStatusIndicatorState extends State<SyncStatusIndicator>
    with TickerProviderStateMixin {
  final BackgroundSyncManager _syncManager = BackgroundSyncManager.instance;

  StreamSubscription<SyncStatus>? _statusSubscription;
  StreamSubscription<SyncProgress>? _progressSubscription;

  SyncStatus _currentStatus = SyncStatus.idle;
  SyncProgress? _currentProgress;
  SyncStatusInfo? _statusInfo;

  late AnimationController _rotationController;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize rotation animation for sync indicator
    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_rotationController);

    // Listen to sync status and progress
    _initializeListeners();

    // Get initial status
    _updateStatusInfo();
  }

  void _initializeListeners() {
    _statusSubscription = _syncManager.statusStream.listen((status) {
      if (mounted) {
        setState(() {
          _currentStatus = status;
          _updateStatusInfo();
        });

        // Start/stop rotation animation based on sync status
        if (status == SyncStatus.syncing) {
          _rotationController.repeat();
        } else {
          _rotationController.stop();
        }
      }
    });

    _progressSubscription = _syncManager.progressStream.listen((progress) {
      if (mounted) {
        setState(() {
          _currentProgress = progress;
        });
      }
    });
  }

  void _updateStatusInfo() {
    _statusInfo = _syncManager.syncStatusInfo;
  }

  @override
  void dispose() {
    _statusSubscription?.cancel();
    _progressSubscription?.cancel();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildSyncIcon(),
          if (widget.showText || widget.showProgress) ...[
            const SizedBox(width: 8),
            _buildSyncInfo(),
          ],
        ],
      ),
    );
  }

  Widget _buildSyncIcon() {
    final statusInfo = _statusInfo ?? _syncManager.syncStatusInfo;

    Widget icon;
    if (_currentStatus == SyncStatus.syncing) {
      icon = AnimatedBuilder(
        animation: _rotationAnimation,
        builder: (context, child) {
          return Transform.rotate(
            angle: _rotationAnimation.value * 2 * 3.14159,
            child: Icon(
              Icons.sync,
              size: widget.iconSize,
              color: statusInfo.statusColor,
            ),
          );
        },
      );
    } else {
      icon = Icon(
        statusInfo.statusIcon,
        size: widget.iconSize,
        color: statusInfo.statusColor,
      );
    }

    return icon;
  }

  Widget _buildSyncInfo() {
    final statusInfo = _statusInfo ?? _syncManager.syncStatusInfo;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showText)
          Text(
            statusInfo.statusText,
            style: TextStyle(
              fontSize: 12,
              color: statusInfo.statusColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        if (widget.showProgress && _currentProgress != null) ...[
          const SizedBox(height: 2),
          _buildProgressIndicator(),
        ],
      ],
    );
  }

  Widget _buildProgressIndicator() {
    if (_currentProgress == null) return const SizedBox.shrink();

    final progress = _currentProgress!;
    final percentage = progress.percentage / 100.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 120,
          height: 4,
          child: LinearProgressIndicator(
            value: percentage,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              _statusInfo?.statusColor ?? Colors.blue,
            ),
          ),
        ),
        const SizedBox(height: 2),
        Text(
          '${progress.operation} (${progress.current}/${progress.total})',
          style: const TextStyle(
            fontSize: 10,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }
}

/// A compact sync status indicator for app bars
class CompactSyncStatusIndicator extends StatelessWidget {
  final double size;

  const CompactSyncStatusIndicator({
    super.key,
    this.size = 20.0,
  });

  @override
  Widget build(BuildContext context) {
    return SyncStatusIndicator(
      showProgress: false,
      showText: false,
      iconSize: size,
      padding: EdgeInsets.zero,
    );
  }
}

/// A detailed sync status card for settings or status pages
class DetailedSyncStatusCard extends StatelessWidget {
  final VoidCallback? onManualSync;

  const DetailedSyncStatusCard({
    super.key,
    this.onManualSync,
  });

  @override
  Widget build(BuildContext context) {
    final syncManager = BackgroundSyncManager.instance;

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.cloud_sync, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'Background Sync',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (onManualSync != null)
                  IconButton(
                    onPressed: syncManager.isSyncing ? null : onManualSync,
                    icon: const Icon(Icons.refresh),
                    tooltip: 'Manual Sync',
                  ),
              ],
            ),
            const SizedBox(height: 16),
            SyncStatusIndicator(
              showProgress: true,
              showText: true,
              iconSize: 20,
              padding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
    );
  }
}
