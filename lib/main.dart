import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as r;
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:schnell_pole_installation/DB/map_model.dart';
import 'package:schnell_pole_installation/Provider/provider.dart';
import 'package:schnell_pole_installation/Pole_Installation_Page/pole_installation.dart';
import 'package:schnell_pole_installation/Update_Page.dart/update_page.dart';
import 'package:schnell_pole_installation/splash_screen/splash_page.dart';
import 'package:schnell_pole_installation/utils/constants.dart';

import 'env.dart';
import 'utils/s3_config.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);
  HttpOverrides.global = MyHttpOverrides();

  // CRITICAL FIX: Use explicit path instead of Hive.initFlutter()
  // This ensures main app and isolate use the exact same Hive database
  final appDocDir = await getApplicationDocumentsDirectory();
  final hivePath = appDocDir.path;

  log('Main app: Initializing Hive with explicit path: $hivePath');
  Hive.init(hivePath);

  // Debug: List Hive files in main app directory
  try {
    final dir = Directory(hivePath);
    final files = dir.listSync().where((f) => f.path.contains('hive')).toList();
    log('Main app: Hive files in directory: ${files.map((f) => f.path.split('/').last).toList()}');
  } catch (e) {
    log('Main app: Could not list Hive files: $e');
  }

  log('currentEnvironment: $currentEnvironment');
  log('baseUrl: $baseUrl');
  log('ticketUrl: $ticketUrl');
  log('Bucket Name: ${S3Config.bucketName}');
  log('S3 Endpoint: ${S3Config.s3Endpoint}');
  runApp(r.ProviderScope(
    child: MultiProvider(providers: [
      ChangeNotifierProvider<PoleCountModel>(create: (_) => PoleCountModel()),
      ChangeNotifierProvider<DataModel>(create: (_) => DataModel()),
    ], child: const MyApp()),
  ));
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'PoleVault',
      debugShowCheckedModeBanner: false,
      theme: lightTheme(context),
      // home:const SplashPage(),
      builder: EasyLoading.init(),
      initialRoute: splashRoute,
      routes: {
        splashRoute: (context) => const SplashPage(),
        updateRoute: (context) => const UpdatePage(),
        dashboardRoute: (context) => const PoleInstallation(),
      },
    );
  }
}
