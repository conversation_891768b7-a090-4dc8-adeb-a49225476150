import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:schnell_pole_installation/survey/models/survey_image_model.dart';

void main() {
  group('Hive Isolate Access Tests', () {
    late String testHivePath;
    late Box surveyBox;
    late Box imagesBox;

    setUpAll(() async {
      // Initialize Flutter binding for testing
      TestWidgetsFlutterBinding.ensureInitialized();

      // Use a simple test path instead of getTemporaryDirectory
      testHivePath = '/tmp/test_hive_${DateTime.now().millisecondsSinceEpoch}';

      // Clean up any existing test data
      final testDir = Directory(testHivePath);
      if (testDir.existsSync()) {
        testDir.deleteSync(recursive: true);
      }

      Hive.init(testHivePath);
    });

    setUp(() async {
      // Open test boxes
      surveyBox = await Hive.openBox('surveyBox');
      imagesBox = await Hive.openBox('imagesBox');
    });

    tearDown(() async {
      // Clean up after each test
      await surveyBox.clear();
      await imagesBox.clear();
      await surveyBox.close();
      await imagesBox.close();
    });

    tearDownAll(() async {
      // Clean up test directory
      final testDir = Directory(testHivePath);
      if (testDir.existsSync()) {
        testDir.deleteSync(recursive: true);
      }
    });

    test('should be able to store and retrieve survey data', () async {
      // Create test survey data
      final testSurveyData = {
        'assetType': 'Pole',
        'roadType': 'Highway',
        'trafficDensity': 'High',
        'latitude': 12.345,
        'longitude': 67.890,
        'isUploaded': false,
        'installedOn': DateTime.now().millisecondsSinceEpoch.toString(),
        'installedBy': 'Test User',
        'region': 'Test Region',
        'zone': 'Test Zone',
        'ward': 'Test Ward',
        'wardId': 'TEST001',
      };

      // Store data in survey box
      await surveyBox.add(testSurveyData);

      // Verify data was stored
      expect(surveyBox.length, equals(1));

      // Retrieve and verify data
      final retrievedData = surveyBox.getAt(0);
      expect(retrievedData, isNotNull);
      expect(retrievedData['assetType'], equals('Pole'));
      expect(retrievedData['roadType'], equals('Highway'));
      expect(retrievedData['isUploaded'], equals(false));
    });

    test('should be able to store and retrieve image data', () async {
      // Create test image data
      final testImageData = MultiCapturedImageModel(
        base64Image1: 'test_base64_image_1',
        fileName1: 'test_image_1.jpg',
        isUploaded1: false,
        base64Image2: 'test_base64_image_2',
        fileName2: 'test_image_2.jpg',
        isUploaded2: false,
        base64Image3: 'test_base64_image_3',
        fileName3: 'test_image_3.jpg',
        isUploaded3: false,
      );

      // Store data in images box
      await imagesBox.add(testImageData.toMap());

      // Verify data was stored
      expect(imagesBox.length, equals(1));

      // Retrieve and verify data
      final retrievedData = imagesBox.getAt(0);
      expect(retrievedData, isNotNull);

      final retrievedModel = MultiCapturedImageModel.fromMap(retrievedData);
      expect(retrievedModel.fileName1, equals('test_image_1.jpg'));
      expect(retrievedModel.fileName2, equals('test_image_2.jpg'));
      expect(retrievedModel.fileName3, equals('test_image_3.jpg'));
      expect(retrievedModel.isUploaded1, equals(false));
      expect(retrievedModel.isUploaded2, equals(false));
      expect(retrievedModel.isUploaded3, equals(false));
    });

    test('should handle multiple survey records', () async {
      // Create multiple test survey records
      final testSurveys = [
        {
          'assetType': 'Pole',
          'roadType': 'Highway',
          'isUploaded': false,
          'installedBy': 'User 1',
        },
        {
          'assetType': 'Switch Point',
          'roadType': 'City Road',
          'isUploaded': true,
          'installedBy': 'User 2',
        },
        {
          'assetType': 'Transformer',
          'roadType': 'Rural Road',
          'isUploaded': false,
          'installedBy': 'User 3',
        },
      ];

      // Store all records
      for (final survey in testSurveys) {
        await surveyBox.add(survey);
      }

      // Verify all records were stored
      expect(surveyBox.length, equals(3));

      // Verify we can retrieve records by asset type
      int poleCount = 0;
      int switchPointCount = 0;
      int transformerCount = 0;

      for (int i = 0; i < surveyBox.length; i++) {
        final data = surveyBox.getAt(i);
        switch (data['assetType']) {
          case 'Pole':
            poleCount++;
            break;
          case 'Switch Point':
            switchPointCount++;
            break;
          case 'Transformer':
            transformerCount++;
            break;
        }
      }

      expect(poleCount, equals(1));
      expect(switchPointCount, equals(1));
      expect(transformerCount, equals(1));
    });

    test('should handle empty boxes gracefully', () async {
      // Verify boxes start empty
      expect(surveyBox.length, equals(0));
      expect(imagesBox.length, equals(0));

      // Verify we can iterate over empty boxes
      expect(surveyBox.keys.length, equals(0));
      expect(imagesBox.keys.length, equals(0));

      // Verify we can safely check if boxes are empty
      expect(surveyBox.isEmpty, isTrue);
      expect(imagesBox.isEmpty, isTrue);

      // Verify we can safely get values with keys that don't exist
      expect(surveyBox.get('nonexistent'), isNull);
      expect(imagesBox.get('nonexistent'), isNull);
    });

    test('should be able to update existing records', () async {
      // Create and store initial data
      final initialData = {
        'assetType': 'Pole',
        'isUploaded': false,
        'installedBy': 'Test User',
      };

      final key = await surveyBox.add(initialData);

      // Verify initial state
      final retrievedData = surveyBox.get(key);
      expect(retrievedData['isUploaded'], equals(false));

      // Update the record
      final updatedData = Map<String, dynamic>.from(retrievedData);
      updatedData['isUploaded'] = true;
      await surveyBox.put(key, updatedData);

      // Verify the update
      final finalData = surveyBox.get(key);
      expect(finalData['isUploaded'], equals(true));
      expect(finalData['installedBy'],
          equals('Test User')); // Other fields unchanged
    });
  });
}
