import 'dart:developer';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';

/// Debug script to check Hive path consistency
void main() async {
  print('=== Hive Path Debug ===');
  
  try {
    // Simulate main app initialization
    print('1. Simulating main app Hive initialization...');
    await Hive.initFlutter();
    
    // Get the path that main app uses
    final appDocDir = await getApplicationDocumentsDirectory();
    final mainAppPath = appDocDir.path;
    print('   Main app Hive path: $mainAppPath');
    
    // Open boxes and add test data
    final surveyBox = await Hive.openBox('surveyBox');
    final imagesBox = await Hive.openBox('imagesBox');
    
    print('   Current surveyBox length: ${surveyBox.length}');
    print('   Current imagesBox length: ${imagesBox.length}');
    
    // Add test data if boxes are empty
    if (surveyBox.isEmpty) {
      await surveyBox.add({
        'assetType': 'Pole',
        'roadType': 'Highway',
        'isUploaded': false,
        'testData': true,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
      print('   Added test survey data');
    }
    
    if (imagesBox.isEmpty) {
      await imagesBox.add({
        'fileName1': 'test_image.jpg',
        'base64Image1': 'test_base64_data',
        'isUploaded1': false,
        'testData': true,
      });
      print('   Added test image data');
    }
    
    print('   Final surveyBox length: ${surveyBox.length}');
    print('   Final imagesBox length: ${imagesBox.length}');
    print('   surveyBox keys: ${surveyBox.keys.toList()}');
    print('   imagesBox keys: ${imagesBox.keys.toList()}');
    
    await surveyBox.close();
    await imagesBox.close();
    
    print('\n2. Simulating isolate Hive access...');
    
    // Simulate what happens in isolate (using same path)
    final isolateBox = await Hive.openBox('surveyBox');
    final isolateImagesBox = await Hive.openBox('imagesBox');
    
    print('   Isolate surveyBox length: ${isolateBox.length}');
    print('   Isolate imagesBox length: ${isolateImagesBox.length}');
    print('   Isolate surveyBox keys: ${isolateBox.keys.toList()}');
    print('   Isolate imagesBox keys: ${isolateImagesBox.keys.toList()}');
    
    if (isolateBox.length > 0) {
      final firstRecord = isolateBox.getAt(0);
      print('   First survey record: $firstRecord');
    }
    
    if (isolateImagesBox.length > 0) {
      final firstImage = isolateImagesBox.getAt(0);
      print('   First image record: $firstImage');
    }
    
    await isolateBox.close();
    await isolateImagesBox.close();
    
    print('\n=== Debug completed ===');
    
  } catch (e) {
    print('Error during debug: $e');
  }
}
